"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-_app-pages-browser_node_modules_goober_dist_goober_modern_js-_app-pages-browser_node_-a9bda9"],{

/***/ "(app-pages-browser)/./node_modules/goober/dist/goober.modern.js":
/*!***************************************************!*\
  !*** ./node_modules/goober/dist/goober.modern.js ***!
  \***************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   css: function() { return /* binding */ u; },\n/* harmony export */   extractCss: function() { return /* binding */ r; },\n/* harmony export */   glob: function() { return /* binding */ b; },\n/* harmony export */   keyframes: function() { return /* binding */ h; },\n/* harmony export */   setup: function() { return /* binding */ m; },\n/* harmony export */   styled: function() { return /* binding */ j; }\n/* harmony export */ });\nlet e={data:\"\"},t=t=>\"object\"==typeof window?((t?t.querySelector(\"#_goober\"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement(\"style\")),{innerHTML:\" \",id:\"_goober\"})).firstChild:t||e,r=e=>{let r=t(e),l=r.data;return r.data=\"\",l},l=/(?:([\\u0080-\\uFFFF\\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\\s*)/g,a=/\\/\\*[^]*?\\*\\/|  +/g,n=/\\n+/g,o=(e,t)=>{let r=\"\",l=\"\",a=\"\";for(let n in e){let c=e[n];\"@\"==n[0]?\"i\"==n[1]?r=n+\" \"+c+\";\":l+=\"f\"==n[1]?o(c,n):n+\"{\"+o(c,\"k\"==n[1]?\"\":t)+\"}\":\"object\"==typeof c?l+=o(c,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\\S+\\([^)]*\\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+\" \"+t:t)):n):null!=c&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,\"-$&\").toLowerCase(),a+=o.p?o.p(n,c):n+\":\"+c+\";\")}return r+(t&&a?t+\"{\"+a+\"}\":a)+l},c={},s=e=>{if(\"object\"==typeof e){let t=\"\";for(let r in e)t+=r+s(e[r]);return t}return e},i=(e,t,r,i,p)=>{let u=s(e),d=c[u]||(c[u]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return\"go\"+r})(u));if(!c[d]){let t=u!==e?e:(e=>{let t,r,o=[{}];for(;t=l.exec(e.replace(a,\"\"));)t[4]?o.shift():t[3]?(r=t[3].replace(n,\" \").trim(),o.unshift(o[0][r]=o[0][r]||{})):o[0][t[1]]=t[2].replace(n,\" \").trim();return o[0]})(e);c[d]=o(p?{[\"@keyframes \"+d]:t}:t,r?\"\":\".\"+d)}let f=r&&c.g?c.g:null;return r&&(c.g=c[d]),((e,t,r,l)=>{l?t.data=t.data.replace(l,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(c[d],t,i,f),d},p=(e,t,r)=>e.reduce((e,l,a)=>{let n=t[a];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?\".\"+t:e&&\"object\"==typeof e?e.props?\"\":o(e,\"\"):!1===e?\"\":e}return e+l+(null==n?\"\":n)},\"\");function u(e){let r=this||{},l=e.call?e(r.p):e;return i(l.unshift?l.raw?p(l,[].slice.call(arguments,1),r.p):l.reduce((e,t)=>Object.assign(e,t&&t.call?t(r.p):t),{}):l,t(r.target),r.g,r.o,r.k)}let d,f,g,b=u.bind({g:1}),h=u.bind({k:1});function m(e,t,r,l){o.p=t,d=e,f=r,g=l}function j(e,t){let r=this||{};return function(){let l=arguments;function a(n,o){let c=Object.assign({},n),s=c.className||a.className;r.p=Object.assign({theme:f&&f()},c),r.o=/ *go\\d+/.test(s),c.className=u.apply(r,l)+(s?\" \"+s:\"\"),t&&(c.ref=o);let i=e;return e[0]&&(i=c.as||e,delete c.as),g&&i[0]&&g(c),d(i,c)}return t?t(a):a}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/goober/dist/goober.modern.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql-request/build/entrypoints/main.js":
/*!****************************************************************!*\
  !*** ./node_modules/graphql-request/build/entrypoints/main.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientError: function() { return /* reexport safe */ _legacy_classes_ClientError_js__WEBPACK_IMPORTED_MODULE_0__.ClientError; },\n/* harmony export */   GraphQLClient: function() { return /* reexport safe */ _legacy_classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_2__.GraphQLClient; },\n/* harmony export */   analyzeDocument: function() { return /* reexport safe */ _legacy_helpers_analyzeDocument_js__WEBPACK_IMPORTED_MODULE_6__.analyzeDocument; },\n/* harmony export */   batchRequests: function() { return /* reexport safe */ _legacy_functions_batchRequests_js__WEBPACK_IMPORTED_MODULE_3__.batchRequests; },\n/* harmony export */   gql: function() { return /* reexport safe */ _legacy_functions_gql_js__WEBPACK_IMPORTED_MODULE_4__.gql; },\n/* harmony export */   rawRequest: function() { return /* reexport safe */ _legacy_functions_rawRequest_js__WEBPACK_IMPORTED_MODULE_5__.rawRequest; },\n/* harmony export */   request: function() { return /* reexport safe */ _legacy_functions_request_js__WEBPACK_IMPORTED_MODULE_1__.request; }\n/* harmony export */ });\n/* harmony import */ var _legacy_classes_ClientError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../legacy/classes/ClientError.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/classes/ClientError.js\");\n/* harmony import */ var _legacy_functions_request_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../legacy/functions/request.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/request.js\");\n/* harmony import */ var _legacy_classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../legacy/classes/GraphQLClient.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/classes/GraphQLClient.js\");\n/* harmony import */ var _legacy_functions_batchRequests_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../legacy/functions/batchRequests.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/batchRequests.js\");\n/* harmony import */ var _legacy_functions_gql_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../legacy/functions/gql.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/gql.js\");\n/* harmony import */ var _legacy_functions_rawRequest_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../legacy/functions/rawRequest.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/rawRequest.js\");\n/* harmony import */ var _legacy_helpers_analyzeDocument_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../legacy/helpers/analyzeDocument.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/helpers/analyzeDocument.js\");\n\n\n\n\n\n\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_legacy_functions_request_js__WEBPACK_IMPORTED_MODULE_1__.request);\n//# sourceMappingURL=main.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsLXJlcXVlc3QvYnVpbGQvZW50cnlwb2ludHMvbWFpbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUErRDtBQUNOO0FBQ1U7QUFDRTtBQUNwQjtBQUNjO0FBQ1E7QUFDdEM7QUFDakMsK0RBQWUsaUVBQU8sRUFBQztBQUN2QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC1yZXF1ZXN0L2J1aWxkL2VudHJ5cG9pbnRzL21haW4uanM/MjdhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDbGllbnRFcnJvciB9IGZyb20gJy4uL2xlZ2FjeS9jbGFzc2VzL0NsaWVudEVycm9yLmpzJztcbmltcG9ydCB7IHJlcXVlc3QgfSBmcm9tICcuLi9sZWdhY3kvZnVuY3Rpb25zL3JlcXVlc3QuanMnO1xuZXhwb3J0IHsgR3JhcGhRTENsaWVudCB9IGZyb20gJy4uL2xlZ2FjeS9jbGFzc2VzL0dyYXBoUUxDbGllbnQuanMnO1xuZXhwb3J0IHsgYmF0Y2hSZXF1ZXN0cyB9IGZyb20gJy4uL2xlZ2FjeS9mdW5jdGlvbnMvYmF0Y2hSZXF1ZXN0cy5qcyc7XG5leHBvcnQgeyBncWwgfSBmcm9tICcuLi9sZWdhY3kvZnVuY3Rpb25zL2dxbC5qcyc7XG5leHBvcnQgeyByYXdSZXF1ZXN0IH0gZnJvbSAnLi4vbGVnYWN5L2Z1bmN0aW9ucy9yYXdSZXF1ZXN0LmpzJztcbmV4cG9ydCB7IGFuYWx5emVEb2N1bWVudCB9IGZyb20gJy4uL2xlZ2FjeS9oZWxwZXJzL2FuYWx5emVEb2N1bWVudC5qcyc7XG5leHBvcnQgeyBDbGllbnRFcnJvciwgcmVxdWVzdCwgfTtcbmV4cG9ydCBkZWZhdWx0IHJlcXVlc3Q7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tYWluLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql-request/build/entrypoints/main.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql-request/build/legacy/classes/ClientError.js":
/*!**************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/classes/ClientError.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientError: function() { return /* binding */ ClientError; }\n/* harmony export */ });\nclass ClientError extends Error {\n    response;\n    request;\n    constructor(response, request) {\n        const message = `${ClientError.extractMessage(response)}: ${JSON.stringify({\n            response,\n            request,\n        })}`;\n        super(message);\n        Object.setPrototypeOf(this, ClientError.prototype);\n        this.response = response;\n        this.request = request;\n        // this is needed as Safari doesn't support .captureStackTrace\n        if (typeof Error.captureStackTrace === `function`) {\n            Error.captureStackTrace(this, ClientError);\n        }\n    }\n    static extractMessage(response) {\n        return response.errors?.[0]?.message ?? `GraphQL Error (Code: ${String(response.status)})`;\n    }\n}\n//# sourceMappingURL=ClientError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsLXJlcXVlc3QvYnVpbGQvbGVnYWN5L2NsYXNzZXMvQ2xpZW50RXJyb3IuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHFDQUFxQyxJQUFJO0FBQ3BFO0FBQ0E7QUFDQSxTQUFTLEVBQUU7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdFQUF3RSx3QkFBd0I7QUFDaEc7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsLXJlcXVlc3QvYnVpbGQvbGVnYWN5L2NsYXNzZXMvQ2xpZW50RXJyb3IuanM/YWNmNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgQ2xpZW50RXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gICAgcmVzcG9uc2U7XG4gICAgcmVxdWVzdDtcbiAgICBjb25zdHJ1Y3RvcihyZXNwb25zZSwgcmVxdWVzdCkge1xuICAgICAgICBjb25zdCBtZXNzYWdlID0gYCR7Q2xpZW50RXJyb3IuZXh0cmFjdE1lc3NhZ2UocmVzcG9uc2UpfTogJHtKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICByZXNwb25zZSxcbiAgICAgICAgICAgIHJlcXVlc3QsXG4gICAgICAgIH0pfWA7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgICBPYmplY3Quc2V0UHJvdG90eXBlT2YodGhpcywgQ2xpZW50RXJyb3IucHJvdG90eXBlKTtcbiAgICAgICAgdGhpcy5yZXNwb25zZSA9IHJlc3BvbnNlO1xuICAgICAgICB0aGlzLnJlcXVlc3QgPSByZXF1ZXN0O1xuICAgICAgICAvLyB0aGlzIGlzIG5lZWRlZCBhcyBTYWZhcmkgZG9lc24ndCBzdXBwb3J0IC5jYXB0dXJlU3RhY2tUcmFjZVxuICAgICAgICBpZiAodHlwZW9mIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlID09PSBgZnVuY3Rpb25gKSB7XG4gICAgICAgICAgICBFcnJvci5jYXB0dXJlU3RhY2tUcmFjZSh0aGlzLCBDbGllbnRFcnJvcik7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGljIGV4dHJhY3RNZXNzYWdlKHJlc3BvbnNlKSB7XG4gICAgICAgIHJldHVybiByZXNwb25zZS5lcnJvcnM/LlswXT8ubWVzc2FnZSA/PyBgR3JhcGhRTCBFcnJvciAoQ29kZTogJHtTdHJpbmcocmVzcG9uc2Uuc3RhdHVzKX0pYDtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1DbGllbnRFcnJvci5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql-request/build/legacy/classes/ClientError.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql-request/build/legacy/classes/GraphQLClient.js":
/*!****************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/classes/GraphQLClient.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GraphQLClient: function() { return /* binding */ GraphQLClient; }\n/* harmony export */ });\n/* harmony import */ var _lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/prelude.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/lib/prelude.js\");\n/* harmony import */ var _functions_batchRequests_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../functions/batchRequests.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/batchRequests.js\");\n/* harmony import */ var _functions_rawRequest_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../functions/rawRequest.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/rawRequest.js\");\n/* harmony import */ var _functions_request_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../functions/request.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/request.js\");\n/* harmony import */ var _helpers_analyzeDocument_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../helpers/analyzeDocument.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/helpers/analyzeDocument.js\");\n/* harmony import */ var _helpers_runRequest_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../helpers/runRequest.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/helpers/runRequest.js\");\n\n\n\n\n\n\n/**\n * GraphQL Client.\n */\nclass GraphQLClient {\n    url;\n    requestConfig;\n    constructor(url, requestConfig = {}) {\n        this.url = url;\n        this.requestConfig = requestConfig;\n    }\n    /**\n     * Send a GraphQL query to the server.\n     */\n    rawRequest = async (...args) => {\n        const [queryOrOptions, variables, requestHeaders] = args;\n        const rawRequestOptions = (0,_functions_rawRequest_js__WEBPACK_IMPORTED_MODULE_2__.parseRawRequestArgs)(queryOrOptions, variables, requestHeaders);\n        const { headers, fetch = globalThis.fetch, method = `POST`, requestMiddleware, responseMiddleware, excludeOperationName, ...fetchOptions } = this.requestConfig;\n        const { url } = this;\n        if (rawRequestOptions.signal !== undefined) {\n            fetchOptions.signal = rawRequestOptions.signal;\n        }\n        const document = (0,_helpers_analyzeDocument_js__WEBPACK_IMPORTED_MODULE_4__.analyzeDocument)(rawRequestOptions.query, excludeOperationName);\n        const response = await (0,_helpers_runRequest_js__WEBPACK_IMPORTED_MODULE_5__.runRequest)({\n            url,\n            request: {\n                _tag: `Single`,\n                document,\n                variables: rawRequestOptions.variables,\n            },\n            headers: {\n                ...(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.HeadersInitToPlainObject)((0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.callOrIdentity)(headers)),\n                ...(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.HeadersInitToPlainObject)(rawRequestOptions.requestHeaders),\n            },\n            fetch,\n            method,\n            fetchOptions,\n            middleware: requestMiddleware,\n        });\n        if (responseMiddleware) {\n            await responseMiddleware(response, {\n                operationName: document.operationName,\n                variables,\n                url: this.url,\n            });\n        }\n        if (response instanceof Error) {\n            throw response;\n        }\n        return response;\n    };\n    async request(documentOrOptions, ...variablesAndRequestHeaders) {\n        const [variables, requestHeaders] = variablesAndRequestHeaders;\n        const requestOptions = (0,_functions_request_js__WEBPACK_IMPORTED_MODULE_3__.parseRequestArgs)(documentOrOptions, variables, requestHeaders);\n        const { headers, fetch = globalThis.fetch, method = `POST`, requestMiddleware, responseMiddleware, excludeOperationName, ...fetchOptions } = this.requestConfig;\n        const { url } = this;\n        if (requestOptions.signal !== undefined) {\n            fetchOptions.signal = requestOptions.signal;\n        }\n        const analyzedDocument = (0,_helpers_analyzeDocument_js__WEBPACK_IMPORTED_MODULE_4__.analyzeDocument)(requestOptions.document, excludeOperationName);\n        const response = await (0,_helpers_runRequest_js__WEBPACK_IMPORTED_MODULE_5__.runRequest)({\n            url,\n            request: {\n                _tag: `Single`,\n                document: analyzedDocument,\n                variables: requestOptions.variables,\n            },\n            headers: {\n                ...(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.HeadersInitToPlainObject)((0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.callOrIdentity)(headers)),\n                ...(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.HeadersInitToPlainObject)(requestOptions.requestHeaders),\n            },\n            fetch,\n            method,\n            fetchOptions,\n            middleware: requestMiddleware,\n        });\n        if (responseMiddleware) {\n            await responseMiddleware(response, {\n                operationName: analyzedDocument.operationName,\n                variables: requestOptions.variables,\n                url: this.url,\n            });\n        }\n        if (response instanceof Error) {\n            throw response;\n        }\n        return response.data;\n    }\n    async batchRequests(documentsOrOptions, requestHeaders) {\n        const batchRequestOptions = (0,_functions_batchRequests_js__WEBPACK_IMPORTED_MODULE_1__.parseBatchRequestArgs)(documentsOrOptions, requestHeaders);\n        const { headers, excludeOperationName, ...fetchOptions } = this.requestConfig;\n        if (batchRequestOptions.signal !== undefined) {\n            fetchOptions.signal = batchRequestOptions.signal;\n        }\n        const analyzedDocuments = batchRequestOptions.documents.map(({ document }) => (0,_helpers_analyzeDocument_js__WEBPACK_IMPORTED_MODULE_4__.analyzeDocument)(document, excludeOperationName));\n        const expressions = analyzedDocuments.map(({ expression }) => expression);\n        const hasMutations = analyzedDocuments.some(({ isMutation }) => isMutation);\n        const variables = batchRequestOptions.documents.map(({ variables }) => variables);\n        const response = await (0,_helpers_runRequest_js__WEBPACK_IMPORTED_MODULE_5__.runRequest)({\n            url: this.url,\n            request: {\n                _tag: `Batch`,\n                operationName: undefined,\n                query: expressions,\n                hasMutations,\n                variables,\n            },\n            headers: {\n                ...(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.HeadersInitToPlainObject)((0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.callOrIdentity)(headers)),\n                ...(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.HeadersInitToPlainObject)(batchRequestOptions.requestHeaders),\n            },\n            fetch: this.requestConfig.fetch ?? globalThis.fetch,\n            method: this.requestConfig.method || `POST`,\n            fetchOptions,\n            middleware: this.requestConfig.requestMiddleware,\n        });\n        if (this.requestConfig.responseMiddleware) {\n            await this.requestConfig.responseMiddleware(response, {\n                operationName: undefined,\n                variables,\n                url: this.url,\n            });\n        }\n        if (response instanceof Error) {\n            throw response;\n        }\n        return response.data;\n    }\n    setHeaders(headers) {\n        this.requestConfig.headers = headers;\n        return this;\n    }\n    /**\n     * Attach a header to the client. All subsequent requests will have this header.\n     */\n    setHeader(key, value) {\n        const { headers } = this.requestConfig;\n        if (headers) {\n            // todo what if headers is in nested array form... ?\n            // @ts-expect-error todo\n            headers[key] = value;\n        }\n        else {\n            this.requestConfig.headers = { [key]: value };\n        }\n        return this;\n    }\n    /**\n     * Change the client endpoint. All subsequent requests will send to this endpoint.\n     */\n    setEndpoint(value) {\n        this.url = value;\n        return this;\n    }\n}\n//# sourceMappingURL=GraphQLClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql-request/build/legacy/classes/GraphQLClient.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/batchRequests.js":
/*!******************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/functions/batchRequests.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   batchRequests: function() { return /* binding */ batchRequests; },\n/* harmony export */   parseBatchRequestArgs: function() { return /* binding */ parseBatchRequestArgs; },\n/* harmony export */   parseBatchRequestsArgsExtended: function() { return /* binding */ parseBatchRequestsArgsExtended; }\n/* harmony export */ });\n/* harmony import */ var _classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../classes/GraphQLClient.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/classes/GraphQLClient.js\");\n\n/**\n * Send a batch of GraphQL Document to the GraphQL server for execution.\n *\n * @example\n *\n * ```ts\n * // You can pass a raw string\n *\n * await batchRequests('https://foo.bar/graphql', [\n * {\n *  query: `\n *   {\n *     query {\n *       users\n *     }\n *   }`\n * },\n * {\n *   query: `\n *   {\n *     query {\n *       users\n *     }\n *   }`\n * }])\n *\n * // You can also pass a GraphQL DocumentNode as query. Convenient if you\n * // are using graphql-tag package.\n *\n * import gql from 'graphql-tag'\n *\n * await batchRequests('https://foo.bar/graphql', [{ query: gql`...` }])\n * ```\n */\nconst batchRequests = async (...args) => {\n    const params = parseBatchRequestsArgsExtended(args);\n    const client = new _classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_0__.GraphQLClient(params.url);\n    return client.batchRequests(params);\n};\nconst parseBatchRequestsArgsExtended = (args) => {\n    if (args.length === 1) {\n        return args[0];\n    }\n    else {\n        return {\n            url: args[0],\n            documents: args[1],\n            requestHeaders: args[2],\n            signal: undefined,\n        };\n    }\n};\nconst parseBatchRequestArgs = (documentsOrOptions, requestHeaders) => {\n    // eslint-disable-next-line\n    return documentsOrOptions.documents\n        ? documentsOrOptions\n        : {\n            documents: documentsOrOptions,\n            requestHeaders: requestHeaders,\n            signal: undefined,\n        };\n};\n//# sourceMappingURL=batchRequests.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/batchRequests.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/gql.js":
/*!********************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/functions/gql.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gql: function() { return /* binding */ gql; }\n/* harmony export */ });\n/**\n * Convenience passthrough template tag to get the benefits of tooling for the gql template tag. This does not actually parse the input into a GraphQL DocumentNode like graphql-tag package does. It just returns the string with any variables given interpolated. Can save you a bit of performance and having to install another package.\n *\n * @example\n * ```\n * import { gql } from 'graphql-request'\n *\n * await request('https://foo.bar/graphql', gql`...`)\n * ```\n *\n * @remarks\n *\n * Several tools in the Node GraphQL ecosystem are hardcoded to specially treat any template tag named \"gql\". For example see this prettier issue: https://github.com/prettier/prettier/issues/4360. Using this template tag has no runtime effect beyond variable interpolation.\n */\nconst gql = (chunks, ...variables) => {\n    return chunks.reduce((acc, chunk, index) => `${acc}${chunk}${index in variables ? String(variables[index]) : ``}`, ``);\n};\n//# sourceMappingURL=gql.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsLXJlcXVlc3QvYnVpbGQvbGVnYWN5L2Z1bmN0aW9ucy9ncWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLE1BQU07QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsbURBQW1ELElBQUksRUFBRSxNQUFNLEVBQUUsbURBQW1EO0FBQ3BIO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2dyYXBocWwtcmVxdWVzdC9idWlsZC9sZWdhY3kvZnVuY3Rpb25zL2dxbC5qcz9iODc5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29udmVuaWVuY2UgcGFzc3Rocm91Z2ggdGVtcGxhdGUgdGFnIHRvIGdldCB0aGUgYmVuZWZpdHMgb2YgdG9vbGluZyBmb3IgdGhlIGdxbCB0ZW1wbGF0ZSB0YWcuIFRoaXMgZG9lcyBub3QgYWN0dWFsbHkgcGFyc2UgdGhlIGlucHV0IGludG8gYSBHcmFwaFFMIERvY3VtZW50Tm9kZSBsaWtlIGdyYXBocWwtdGFnIHBhY2thZ2UgZG9lcy4gSXQganVzdCByZXR1cm5zIHRoZSBzdHJpbmcgd2l0aCBhbnkgdmFyaWFibGVzIGdpdmVuIGludGVycG9sYXRlZC4gQ2FuIHNhdmUgeW91IGEgYml0IG9mIHBlcmZvcm1hbmNlIGFuZCBoYXZpbmcgdG8gaW5zdGFsbCBhbm90aGVyIHBhY2thZ2UuXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYFxuICogaW1wb3J0IHsgZ3FsIH0gZnJvbSAnZ3JhcGhxbC1yZXF1ZXN0J1xuICpcbiAqIGF3YWl0IHJlcXVlc3QoJ2h0dHBzOi8vZm9vLmJhci9ncmFwaHFsJywgZ3FsYC4uLmApXG4gKiBgYGBcbiAqXG4gKiBAcmVtYXJrc1xuICpcbiAqIFNldmVyYWwgdG9vbHMgaW4gdGhlIE5vZGUgR3JhcGhRTCBlY29zeXN0ZW0gYXJlIGhhcmRjb2RlZCB0byBzcGVjaWFsbHkgdHJlYXQgYW55IHRlbXBsYXRlIHRhZyBuYW1lZCBcImdxbFwiLiBGb3IgZXhhbXBsZSBzZWUgdGhpcyBwcmV0dGllciBpc3N1ZTogaHR0cHM6Ly9naXRodWIuY29tL3ByZXR0aWVyL3ByZXR0aWVyL2lzc3Vlcy80MzYwLiBVc2luZyB0aGlzIHRlbXBsYXRlIHRhZyBoYXMgbm8gcnVudGltZSBlZmZlY3QgYmV5b25kIHZhcmlhYmxlIGludGVycG9sYXRpb24uXG4gKi9cbmV4cG9ydCBjb25zdCBncWwgPSAoY2h1bmtzLCAuLi52YXJpYWJsZXMpID0+IHtcbiAgICByZXR1cm4gY2h1bmtzLnJlZHVjZSgoYWNjLCBjaHVuaywgaW5kZXgpID0+IGAke2FjY30ke2NodW5rfSR7aW5kZXggaW4gdmFyaWFibGVzID8gU3RyaW5nKHZhcmlhYmxlc1tpbmRleF0pIDogYGB9YCwgYGApO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdxbC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/gql.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/rawRequest.js":
/*!***************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/functions/rawRequest.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseRawRequestArgs: function() { return /* binding */ parseRawRequestArgs; },\n/* harmony export */   parseRawRequestExtendedArgs: function() { return /* binding */ parseRawRequestExtendedArgs; },\n/* harmony export */   rawRequest: function() { return /* binding */ rawRequest; }\n/* harmony export */ });\n/* harmony import */ var _classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../classes/GraphQLClient.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/classes/GraphQLClient.js\");\n\n/**\n * Send a GraphQL Query to the GraphQL server for execution.\n */\nconst rawRequest = async (...args) => {\n    const [urlOrOptions, query, ...variablesAndRequestHeaders] = args;\n    const requestOptions = parseRawRequestExtendedArgs(urlOrOptions, query, ...variablesAndRequestHeaders);\n    const client = new _classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_0__.GraphQLClient(requestOptions.url);\n    return client.rawRequest({\n        ...requestOptions,\n    });\n};\nconst parseRawRequestExtendedArgs = (urlOrOptions, query, ...variablesAndRequestHeaders) => {\n    const [variables, requestHeaders] = variablesAndRequestHeaders;\n    return typeof urlOrOptions === `string`\n        ? {\n            url: urlOrOptions,\n            query: query,\n            variables,\n            requestHeaders,\n            signal: undefined,\n        }\n        : urlOrOptions;\n};\nconst parseRawRequestArgs = (queryOrOptions, variables, requestHeaders) => {\n    return queryOrOptions.query\n        ? queryOrOptions\n        : {\n            query: queryOrOptions,\n            variables: variables,\n            requestHeaders: requestHeaders,\n            signal: undefined,\n        };\n};\n//# sourceMappingURL=rawRequest.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/rawRequest.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/request.js":
/*!************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/functions/request.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseRequestArgs: function() { return /* binding */ parseRequestArgs; },\n/* harmony export */   parseRequestExtendedArgs: function() { return /* binding */ parseRequestExtendedArgs; },\n/* harmony export */   request: function() { return /* binding */ request; }\n/* harmony export */ });\n/* harmony import */ var _classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../classes/GraphQLClient.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/classes/GraphQLClient.js\");\n\n// dprint-ignore\n// eslint-disable-next-line\nasync function request(urlOrOptions, document, ...variablesAndRequestHeaders) {\n    const requestOptions = parseRequestExtendedArgs(urlOrOptions, document, ...variablesAndRequestHeaders);\n    const client = new _classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_0__.GraphQLClient(requestOptions.url);\n    return client.request({\n        ...requestOptions,\n    });\n}\nconst parseRequestArgs = (documentOrOptions, variables, requestHeaders) => {\n    return documentOrOptions.document\n        ? documentOrOptions\n        : {\n            document: documentOrOptions,\n            variables: variables,\n            requestHeaders: requestHeaders,\n            signal: undefined,\n        };\n};\nconst parseRequestExtendedArgs = (urlOrOptions, document, ...variablesAndRequestHeaders) => {\n    const [variables, requestHeaders] = variablesAndRequestHeaders;\n    return typeof urlOrOptions === `string`\n        ? {\n            url: urlOrOptions,\n            document: document,\n            variables,\n            requestHeaders,\n            signal: undefined,\n        }\n        : urlOrOptions;\n};\n//# sourceMappingURL=request.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsLXJlcXVlc3QvYnVpbGQvbGVnYWN5L2Z1bmN0aW9ucy9yZXF1ZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNEQ7QUFDNUQ7QUFDQTtBQUNPO0FBQ1A7QUFDQSx1QkFBdUIsb0VBQWE7QUFDcEM7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2dyYXBocWwtcmVxdWVzdC9idWlsZC9sZWdhY3kvZnVuY3Rpb25zL3JlcXVlc3QuanM/NWFlOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBHcmFwaFFMQ2xpZW50IH0gZnJvbSAnLi4vY2xhc3Nlcy9HcmFwaFFMQ2xpZW50LmpzJztcbi8vIGRwcmludC1pZ25vcmVcbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHJlcXVlc3QodXJsT3JPcHRpb25zLCBkb2N1bWVudCwgLi4udmFyaWFibGVzQW5kUmVxdWVzdEhlYWRlcnMpIHtcbiAgICBjb25zdCByZXF1ZXN0T3B0aW9ucyA9IHBhcnNlUmVxdWVzdEV4dGVuZGVkQXJncyh1cmxPck9wdGlvbnMsIGRvY3VtZW50LCAuLi52YXJpYWJsZXNBbmRSZXF1ZXN0SGVhZGVycyk7XG4gICAgY29uc3QgY2xpZW50ID0gbmV3IEdyYXBoUUxDbGllbnQocmVxdWVzdE9wdGlvbnMudXJsKTtcbiAgICByZXR1cm4gY2xpZW50LnJlcXVlc3Qoe1xuICAgICAgICAuLi5yZXF1ZXN0T3B0aW9ucyxcbiAgICB9KTtcbn1cbmV4cG9ydCBjb25zdCBwYXJzZVJlcXVlc3RBcmdzID0gKGRvY3VtZW50T3JPcHRpb25zLCB2YXJpYWJsZXMsIHJlcXVlc3RIZWFkZXJzKSA9PiB7XG4gICAgcmV0dXJuIGRvY3VtZW50T3JPcHRpb25zLmRvY3VtZW50XG4gICAgICAgID8gZG9jdW1lbnRPck9wdGlvbnNcbiAgICAgICAgOiB7XG4gICAgICAgICAgICBkb2N1bWVudDogZG9jdW1lbnRPck9wdGlvbnMsXG4gICAgICAgICAgICB2YXJpYWJsZXM6IHZhcmlhYmxlcyxcbiAgICAgICAgICAgIHJlcXVlc3RIZWFkZXJzOiByZXF1ZXN0SGVhZGVycyxcbiAgICAgICAgICAgIHNpZ25hbDogdW5kZWZpbmVkLFxuICAgICAgICB9O1xufTtcbmV4cG9ydCBjb25zdCBwYXJzZVJlcXVlc3RFeHRlbmRlZEFyZ3MgPSAodXJsT3JPcHRpb25zLCBkb2N1bWVudCwgLi4udmFyaWFibGVzQW5kUmVxdWVzdEhlYWRlcnMpID0+IHtcbiAgICBjb25zdCBbdmFyaWFibGVzLCByZXF1ZXN0SGVhZGVyc10gPSB2YXJpYWJsZXNBbmRSZXF1ZXN0SGVhZGVycztcbiAgICByZXR1cm4gdHlwZW9mIHVybE9yT3B0aW9ucyA9PT0gYHN0cmluZ2BcbiAgICAgICAgPyB7XG4gICAgICAgICAgICB1cmw6IHVybE9yT3B0aW9ucyxcbiAgICAgICAgICAgIGRvY3VtZW50OiBkb2N1bWVudCxcbiAgICAgICAgICAgIHZhcmlhYmxlcyxcbiAgICAgICAgICAgIHJlcXVlc3RIZWFkZXJzLFxuICAgICAgICAgICAgc2lnbmFsOiB1bmRlZmluZWQsXG4gICAgICAgIH1cbiAgICAgICAgOiB1cmxPck9wdGlvbnM7XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVxdWVzdC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql-request/build/legacy/functions/request.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql-request/build/legacy/helpers/analyzeDocument.js":
/*!******************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/helpers/analyzeDocument.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeDocument: function() { return /* binding */ analyzeDocument; }\n/* harmony export */ });\n/* harmony import */ var _lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/prelude.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/lib/prelude.js\");\n/* harmony import */ var _lib_graphql_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/graphql.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/lib/graphql.js\");\n/* harmony import */ var graphql__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! graphql */ \"(app-pages-browser)/./node_modules/graphql/language/parser.mjs\");\n/* harmony import */ var graphql__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! graphql */ \"(app-pages-browser)/./node_modules/graphql/language/printer.mjs\");\n\n\n\n\n/**\n * helpers\n */\nconst extractOperationName = (document) => {\n    let operationName = undefined;\n    const defs = document.definitions.filter(_lib_graphql_js__WEBPACK_IMPORTED_MODULE_1__.isOperationDefinitionNode);\n    if (defs.length === 1) {\n        operationName = defs[0].name?.value;\n    }\n    return operationName;\n};\nconst extractIsMutation = (document) => {\n    let isMutation = false;\n    const defs = document.definitions.filter(_lib_graphql_js__WEBPACK_IMPORTED_MODULE_1__.isOperationDefinitionNode);\n    if (defs.length === 1) {\n        /* eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison --\n         * graphql@15's `OperationTypeNode` is a type, but graphql@16's `OperationTypeNode` is a native TypeScript enum\n         * Therefore, we cannot use `OperationTypeNode.MUTATION` here because it wouldn't work with graphql@15\n         **/\n        isMutation = defs[0].operation === `mutation`;\n    }\n    return isMutation;\n};\nconst analyzeDocument = (document, excludeOperationName) => {\n    const expression = typeof document === `string` ? document : (0,graphql__WEBPACK_IMPORTED_MODULE_2__.print)(document);\n    let isMutation = false;\n    let operationName = undefined;\n    if (excludeOperationName) {\n        return { expression, isMutation, operationName };\n    }\n    const docNode = (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.tryCatch)(() => (typeof document === `string` ? (0,graphql__WEBPACK_IMPORTED_MODULE_3__.parse)(document) : document));\n    if (docNode instanceof Error) {\n        return { expression, isMutation, operationName };\n    }\n    operationName = extractOperationName(docNode);\n    isMutation = extractIsMutation(docNode);\n    return { expression, operationName, isMutation };\n};\n//# sourceMappingURL=analyzeDocument.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql-request/build/legacy/helpers/analyzeDocument.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql-request/build/legacy/helpers/defaultJsonSerializer.js":
/*!************************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/helpers/defaultJsonSerializer.js ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultJsonSerializer: function() { return /* binding */ defaultJsonSerializer; }\n/* harmony export */ });\nconst defaultJsonSerializer = JSON;\n//# sourceMappingURL=defaultJsonSerializer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsLXJlcXVlc3QvYnVpbGQvbGVnYWN5L2hlbHBlcnMvZGVmYXVsdEpzb25TZXJpYWxpemVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsLXJlcXVlc3QvYnVpbGQvbGVnYWN5L2hlbHBlcnMvZGVmYXVsdEpzb25TZXJpYWxpemVyLmpzPzIzMDkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGRlZmF1bHRKc29uU2VyaWFsaXplciA9IEpTT047XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kZWZhdWx0SnNvblNlcmlhbGl6ZXIuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql-request/build/legacy/helpers/defaultJsonSerializer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql-request/build/legacy/helpers/runRequest.js":
/*!*************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/helpers/runRequest.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   runRequest: function() { return /* binding */ runRequest; }\n/* harmony export */ });\n/* harmony import */ var _lib_http_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/http.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/lib/http.js\");\n/* harmony import */ var _lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/prelude.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/lib/prelude.js\");\n/* harmony import */ var _classes_ClientError_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../classes/ClientError.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/classes/ClientError.js\");\n/* harmony import */ var _lib_graphql_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/graphql.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/lib/graphql.js\");\n/* harmony import */ var _defaultJsonSerializer_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./defaultJsonSerializer.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/legacy/helpers/defaultJsonSerializer.js\");\n\n\n\n\n\n// @ts-expect-error todo\nconst runRequest = async (input) => {\n    // todo make a Config type\n    const config = {\n        ...input,\n        method: input.request._tag === `Single`\n            ? input.request.document.isMutation\n                ? `POST`\n                : (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.uppercase)(input.method ?? `post`)\n            : input.request.hasMutations\n                ? `POST`\n                : (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.uppercase)(input.method ?? `post`),\n        fetchOptions: {\n            ...input.fetchOptions,\n            errorPolicy: input.fetchOptions.errorPolicy ?? `none`,\n        },\n    };\n    const fetcher = createFetcher(config.method);\n    const fetchResponse = await fetcher(config);\n    if (!fetchResponse.ok) {\n        return new _classes_ClientError_js__WEBPACK_IMPORTED_MODULE_2__.ClientError({ status: fetchResponse.status, headers: fetchResponse.headers }, {\n            query: input.request._tag === `Single` ? input.request.document.expression : input.request.query,\n            variables: input.request.variables,\n        });\n    }\n    const result = await parseResultFromResponse(fetchResponse, input.fetchOptions.jsonSerializer ?? _defaultJsonSerializer_js__WEBPACK_IMPORTED_MODULE_4__.defaultJsonSerializer);\n    if (result instanceof Error)\n        throw result; // todo something better\n    const clientResponseBase = {\n        status: fetchResponse.status,\n        headers: fetchResponse.headers,\n    };\n    if ((0,_lib_graphql_js__WEBPACK_IMPORTED_MODULE_3__.isRequestResultHaveErrors)(result) && config.fetchOptions.errorPolicy === `none`) {\n        // todo this client response on error is not consistent with the data type for success\n        const clientResponse = result._tag === `Batch`\n            ? { ...result.executionResults, ...clientResponseBase }\n            : {\n                ...result.executionResult,\n                ...clientResponseBase,\n            };\n        // @ts-expect-error todo\n        return new _classes_ClientError_js__WEBPACK_IMPORTED_MODULE_2__.ClientError(clientResponse, {\n            query: input.request._tag === `Single` ? input.request.document.expression : input.request.query,\n            variables: input.request.variables,\n        });\n    }\n    switch (result._tag) {\n        case `Single`:\n            // @ts-expect-error todo\n            return {\n                ...clientResponseBase,\n                ...executionResultClientResponseFields(config)(result.executionResult),\n            };\n        case `Batch`:\n            return {\n                ...clientResponseBase,\n                data: result.executionResults.map(executionResultClientResponseFields(config)),\n            };\n        default:\n            (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.casesExhausted)(result);\n    }\n};\nconst executionResultClientResponseFields = ($params) => (executionResult) => {\n    return {\n        extensions: executionResult.extensions,\n        data: executionResult.data,\n        errors: $params.fetchOptions.errorPolicy === `all` ? executionResult.errors : undefined,\n    };\n};\nconst parseResultFromResponse = async (response, jsonSerializer) => {\n    const contentType = response.headers.get(_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_HEADER);\n    const text = await response.text();\n    if (contentType && (0,_lib_graphql_js__WEBPACK_IMPORTED_MODULE_3__.isGraphQLContentType)(contentType)) {\n        return (0,_lib_graphql_js__WEBPACK_IMPORTED_MODULE_3__.parseGraphQLExecutionResult)(jsonSerializer.parse(text));\n    }\n    else {\n        // todo what is this good for...? Seems very random/undefined\n        return (0,_lib_graphql_js__WEBPACK_IMPORTED_MODULE_3__.parseGraphQLExecutionResult)(text);\n    }\n};\nconst createFetcher = (method) => async (params) => {\n    const headers = new Headers(params.headers);\n    let searchParams = null;\n    let body = undefined;\n    if (!headers.has(_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.ACCEPT_HEADER)) {\n        headers.set(_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.ACCEPT_HEADER, [_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_GQL, _lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_JSON].join(`, `));\n    }\n    if (method === `POST`) {\n        const $jsonSerializer = params.fetchOptions.jsonSerializer ?? _defaultJsonSerializer_js__WEBPACK_IMPORTED_MODULE_4__.defaultJsonSerializer;\n        body = $jsonSerializer.stringify(buildBody(params));\n        if (typeof body === `string` && !headers.has(_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_HEADER)) {\n            headers.set(_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_HEADER, _lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_JSON);\n        }\n    }\n    else {\n        searchParams = buildQueryParams(params);\n    }\n    const init = { method, headers, body, ...params.fetchOptions };\n    let url = new URL(params.url);\n    let initResolved = init;\n    if (params.middleware) {\n        const result = await Promise.resolve(params.middleware({\n            ...init,\n            url: params.url,\n            operationName: params.request._tag === `Single` ? params.request.document.operationName : undefined,\n            variables: params.request.variables,\n        }));\n        const { url: urlNew, ...initNew } = result;\n        url = new URL(urlNew);\n        initResolved = initNew;\n    }\n    if (searchParams) {\n        searchParams.forEach((value, name) => {\n            url.searchParams.append(name, value);\n        });\n    }\n    const $fetch = params.fetch ?? fetch;\n    return await $fetch(url, initResolved);\n};\nconst buildBody = (params) => {\n    switch (params.request._tag) {\n        case `Single`:\n            return {\n                query: params.request.document.expression,\n                variables: params.request.variables,\n                operationName: params.request.document.operationName,\n            };\n        case `Batch`:\n            return (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.zip)(params.request.query, params.request.variables ?? []).map(([query, variables]) => ({\n                query,\n                variables,\n            }));\n        default:\n            throw (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.casesExhausted)(params.request);\n    }\n};\nconst buildQueryParams = (params) => {\n    const $jsonSerializer = params.fetchOptions.jsonSerializer ?? _defaultJsonSerializer_js__WEBPACK_IMPORTED_MODULE_4__.defaultJsonSerializer;\n    const searchParams = new URLSearchParams();\n    switch (params.request._tag) {\n        case `Single`: {\n            searchParams.append(`query`, (0,_lib_graphql_js__WEBPACK_IMPORTED_MODULE_3__.cleanQuery)(params.request.document.expression));\n            if (params.request.variables) {\n                searchParams.append(`variables`, $jsonSerializer.stringify(params.request.variables));\n            }\n            if (params.request.document.operationName) {\n                searchParams.append(`operationName`, params.request.document.operationName);\n            }\n            return searchParams;\n        }\n        case `Batch`: {\n            const variablesSerialized = params.request.variables?.map((v) => $jsonSerializer.stringify(v)) ?? [];\n            const queriesCleaned = params.request.query.map(_lib_graphql_js__WEBPACK_IMPORTED_MODULE_3__.cleanQuery);\n            const payload = (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.zip)(queriesCleaned, variablesSerialized).map(([query, variables]) => ({\n                query,\n                variables,\n            }));\n            searchParams.append(`query`, $jsonSerializer.stringify(payload));\n            return searchParams;\n        }\n        default:\n            throw (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.casesExhausted)(params.request);\n    }\n};\n//# sourceMappingURL=runRequest.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql-request/build/legacy/helpers/runRequest.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql-request/build/legacy/lib/graphql.js":
/*!******************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/lib/graphql.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanQuery: function() { return /* binding */ cleanQuery; },\n/* harmony export */   isExecutionResultHaveErrors: function() { return /* binding */ isExecutionResultHaveErrors; },\n/* harmony export */   isGraphQLContentType: function() { return /* binding */ isGraphQLContentType; },\n/* harmony export */   isOperationDefinitionNode: function() { return /* binding */ isOperationDefinitionNode; },\n/* harmony export */   isRequestResultHaveErrors: function() { return /* binding */ isRequestResultHaveErrors; },\n/* harmony export */   parseExecutionResult: function() { return /* binding */ parseExecutionResult; },\n/* harmony export */   parseGraphQLExecutionResult: function() { return /* binding */ parseGraphQLExecutionResult; }\n/* harmony export */ });\n/* harmony import */ var graphql__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! graphql */ \"(app-pages-browser)/./node_modules/graphql/language/kinds.mjs\");\n/* harmony import */ var _lib_http_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/http.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/lib/http.js\");\n/* harmony import */ var _lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/prelude.js */ \"(app-pages-browser)/./node_modules/graphql-request/build/lib/prelude.js\");\n\n\n\n/**\n * Clean a GraphQL document to send it via a GET query\n */\nconst cleanQuery = (str) => str.replace(/([\\s,]|#[^\\n\\r]+)+/g, ` `).trim();\nconst isGraphQLContentType = (contentType) => {\n    const contentTypeLower = contentType.toLowerCase();\n    return contentTypeLower.includes(_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_GQL) || contentTypeLower.includes(_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_JSON);\n};\nconst parseGraphQLExecutionResult = (result) => {\n    try {\n        if (Array.isArray(result)) {\n            return {\n                _tag: `Batch`,\n                executionResults: result.map(parseExecutionResult),\n            };\n        }\n        else if ((0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.isPlainObject)(result)) {\n            return {\n                _tag: `Single`,\n                executionResult: parseExecutionResult(result),\n            };\n        }\n        else {\n            throw new Error(`Invalid execution result: result is not object or array. \\nGot:\\n${String(result)}`);\n        }\n    }\n    catch (e) {\n        return e;\n    }\n};\n/**\n * Example result:\n *\n * ```\n * {\n *  \"data\": null,\n *  \"errors\": [{\n *    \"message\": \"custom error\",\n *    \"locations\": [{ \"line\": 2, \"column\": 3 }],\n *    \"path\": [\"playerNew\"]\n *  }]\n * }\n * ```\n */\nconst parseExecutionResult = (result) => {\n    if (typeof result !== `object` || result === null) {\n        throw new Error(`Invalid execution result: result is not object`);\n    }\n    let errors = undefined;\n    let data = undefined;\n    let extensions = undefined;\n    if (`errors` in result) {\n        if (!(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.isPlainObject)(result.errors) && !Array.isArray(result.errors)) {\n            throw new Error(`Invalid execution result: errors is not plain object OR array`); // prettier-ignore\n        }\n        errors = result.errors;\n    }\n    // todo add test coverage for case of null. @see https://github.com/jasonkuhrt/graphql-request/issues/739\n    if (`data` in result) {\n        if (!(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.isPlainObject)(result.data) && result.data !== null) {\n            throw new Error(`Invalid execution result: data is not plain object`); // prettier-ignore\n        }\n        data = result.data;\n    }\n    if (`extensions` in result) {\n        if (!(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.isPlainObject)(result.extensions))\n            throw new Error(`Invalid execution result: extensions is not plain object`); // prettier-ignore\n        extensions = result.extensions;\n    }\n    return {\n        data,\n        errors,\n        extensions,\n    };\n};\nconst isRequestResultHaveErrors = (result) => result._tag === `Batch`\n    ? result.executionResults.some(isExecutionResultHaveErrors)\n    : isExecutionResultHaveErrors(result.executionResult);\nconst isExecutionResultHaveErrors = (result) => Array.isArray(result.errors) ? result.errors.length > 0 : Boolean(result.errors);\nconst isOperationDefinitionNode = (definition) => {\n    return (typeof definition === `object`\n        && definition !== null\n        && `kind` in definition\n        && definition.kind === graphql__WEBPACK_IMPORTED_MODULE_2__.Kind.OPERATION_DEFINITION);\n};\n//# sourceMappingURL=graphql.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql-request/build/legacy/lib/graphql.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql-request/build/lib/http.js":
/*!********************************************************!*\
  !*** ./node_modules/graphql-request/build/lib/http.js ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACCEPT_HEADER: function() { return /* binding */ ACCEPT_HEADER; },\n/* harmony export */   CONTENT_TYPE_GQL: function() { return /* binding */ CONTENT_TYPE_GQL; },\n/* harmony export */   CONTENT_TYPE_HEADER: function() { return /* binding */ CONTENT_TYPE_HEADER; },\n/* harmony export */   CONTENT_TYPE_JSON: function() { return /* binding */ CONTENT_TYPE_JSON; },\n/* harmony export */   statusCodes: function() { return /* binding */ statusCodes; }\n/* harmony export */ });\nconst ACCEPT_HEADER = `Accept`;\nconst CONTENT_TYPE_HEADER = `Content-Type`;\nconst CONTENT_TYPE_JSON = `application/json`;\nconst CONTENT_TYPE_GQL = `application/graphql-response+json`;\nconst statusCodes = {\n    success: 200,\n};\n//# sourceMappingURL=http.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsLXJlcXVlc3QvYnVpbGQvbGliL2h0dHAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBTztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsLXJlcXVlc3QvYnVpbGQvbGliL2h0dHAuanM/M2ZlMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgQUNDRVBUX0hFQURFUiA9IGBBY2NlcHRgO1xuZXhwb3J0IGNvbnN0IENPTlRFTlRfVFlQRV9IRUFERVIgPSBgQ29udGVudC1UeXBlYDtcbmV4cG9ydCBjb25zdCBDT05URU5UX1RZUEVfSlNPTiA9IGBhcHBsaWNhdGlvbi9qc29uYDtcbmV4cG9ydCBjb25zdCBDT05URU5UX1RZUEVfR1FMID0gYGFwcGxpY2F0aW9uL2dyYXBocWwtcmVzcG9uc2UranNvbmA7XG5leHBvcnQgY29uc3Qgc3RhdHVzQ29kZXMgPSB7XG4gICAgc3VjY2VzczogMjAwLFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWh0dHAuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql-request/build/lib/http.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql-request/build/lib/prelude.js":
/*!***********************************************************!*\
  !*** ./node_modules/graphql-request/build/lib/prelude.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeadersInitToPlainObject: function() { return /* binding */ HeadersInitToPlainObject; },\n/* harmony export */   HeadersInstanceToPlainObject: function() { return /* binding */ HeadersInstanceToPlainObject; },\n/* harmony export */   assertArray: function() { return /* binding */ assertArray; },\n/* harmony export */   assertObject: function() { return /* binding */ assertObject; },\n/* harmony export */   callOrIdentity: function() { return /* binding */ callOrIdentity; },\n/* harmony export */   capitalizeFirstLetter: function() { return /* binding */ capitalizeFirstLetter; },\n/* harmony export */   casesExhausted: function() { return /* binding */ casesExhausted; },\n/* harmony export */   createDeferred: function() { return /* binding */ createDeferred; },\n/* harmony export */   debug: function() { return /* binding */ debug; },\n/* harmony export */   debugSub: function() { return /* binding */ debugSub; },\n/* harmony export */   entries: function() { return /* binding */ entries; },\n/* harmony export */   errorFromMaybeError: function() { return /* binding */ errorFromMaybeError; },\n/* harmony export */   isPlainObject: function() { return /* binding */ isPlainObject; },\n/* harmony export */   isPromiseLikeValue: function() { return /* binding */ isPromiseLikeValue; },\n/* harmony export */   lowerCaseFirstLetter: function() { return /* binding */ lowerCaseFirstLetter; },\n/* harmony export */   mapValues: function() { return /* binding */ mapValues; },\n/* harmony export */   partitionErrors: function() { return /* binding */ partitionErrors; },\n/* harmony export */   tryCatch: function() { return /* binding */ tryCatch; },\n/* harmony export */   uppercase: function() { return /* binding */ uppercase; },\n/* harmony export */   values: function() { return /* binding */ values; },\n/* harmony export */   zip: function() { return /* binding */ zip; }\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\nconst uppercase = (str) => str.toUpperCase();\nconst callOrIdentity = (value) => {\n    return typeof value === `function` ? value() : value;\n};\nconst zip = (a, b) => a.map((k, i) => [k, b[i]]);\nconst HeadersInitToPlainObject = (headers) => {\n    let oHeaders = {};\n    if (headers instanceof Headers) {\n        oHeaders = HeadersInstanceToPlainObject(headers);\n    }\n    else if (Array.isArray(headers)) {\n        headers.forEach(([name, value]) => {\n            if (name && value !== undefined) {\n                oHeaders[name] = value;\n            }\n        });\n    }\n    else if (headers) {\n        oHeaders = headers;\n    }\n    return oHeaders;\n};\nconst HeadersInstanceToPlainObject = (headers) => {\n    const o = {};\n    headers.forEach((v, k) => {\n        o[k] = v;\n    });\n    return o;\n};\nconst tryCatch = (fn) => {\n    try {\n        const result = fn();\n        if (isPromiseLikeValue(result)) {\n            return result.catch((error) => {\n                return errorFromMaybeError(error);\n            });\n        }\n        return result;\n    }\n    catch (error) {\n        return errorFromMaybeError(error);\n    }\n};\n/**\n * Ensure that the given value is an error and return it. If it is not an error than\n * wrap it in one, passing the given value as the error message.\n */\nconst errorFromMaybeError = (maybeError) => {\n    if (maybeError instanceof Error)\n        return maybeError;\n    return new Error(String(maybeError));\n};\nconst isPromiseLikeValue = (value) => {\n    return (typeof value === `object`\n        && value !== null\n        && `then` in value\n        && typeof value.then === `function`\n        && `catch` in value\n        && typeof value.catch === `function`\n        && `finally` in value\n        && typeof value.finally === `function`);\n};\nconst casesExhausted = (value) => {\n    throw new Error(`Unhandled case: ${String(value)}`);\n};\nconst isPlainObject = (value) => {\n    return typeof value === `object` && value !== null && !Array.isArray(value);\n};\nconst entries = (obj) => Object.entries(obj);\nconst values = (obj) => Object.values(obj);\nconst mapValues = (object, fn) => {\n    return Object.fromEntries(Object.entries(object).map(([key, value]) => {\n        return [key, fn(value, key)];\n    }));\n};\nconst lowerCaseFirstLetter = (s) => {\n    return s.charAt(0).toLowerCase() + s.slice(1);\n};\nfunction assertArray(v) {\n    if (!Array.isArray(v))\n        throw new Error(`Expected array. Got: ${String(v)}`);\n}\nfunction assertObject(v) {\n    if (v === null || typeof v !== `object`)\n        throw new Error(`Expected object. Got: ${String(v)}`);\n}\nconst capitalizeFirstLetter = (string) => string.charAt(0).toUpperCase() + string.slice(1);\nconst createDeferred = (options) => {\n    let isResolved = false;\n    let resolve;\n    let reject;\n    const promise = new Promise(($resolve, $reject) => {\n        resolve = $resolve;\n        reject = $reject;\n    });\n    return {\n        promise,\n        isResolved: () => isResolved,\n        resolve: (value) => {\n            isResolved = true;\n            if (options?.strict && isResolved) {\n                throw new Error(`Deferred is already resolved. Attempted to resolve with: ${JSON.stringify(value)}`);\n            }\n            resolve(value);\n        },\n        reject: (error) => reject(error),\n    };\n};\nconst debug = (...args) => {\n    if (process.env[`DEBUG`]) {\n        console.log(...args);\n    }\n};\nconst debugSub = (...args) => (...subArgs) => {\n    debug(...args, ...subArgs);\n};\nconst partitionErrors = (array) => {\n    const errors = [];\n    const values = [];\n    for (const item of array) {\n        if (item instanceof Error) {\n            errors.push(item);\n        }\n        else {\n            values.push(item);\n        }\n    }\n    return [values, errors];\n};\n//# sourceMappingURL=prelude.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql-request/build/lib/prelude.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql/error/GraphQLError.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/graphql/error/GraphQLError.mjs ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GraphQLError: function() { return /* binding */ GraphQLError; },\n/* harmony export */   formatError: function() { return /* binding */ formatError; },\n/* harmony export */   printError: function() { return /* binding */ printError; }\n/* harmony export */ });\n/* harmony import */ var _jsutils_isObjectLike_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../jsutils/isObjectLike.mjs */ \"(app-pages-browser)/./node_modules/graphql/jsutils/isObjectLike.mjs\");\n/* harmony import */ var _language_location_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../language/location.mjs */ \"(app-pages-browser)/./node_modules/graphql/language/location.mjs\");\n/* harmony import */ var _language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../language/printLocation.mjs */ \"(app-pages-browser)/./node_modules/graphql/language/printLocation.mjs\");\n\n\n\n\nfunction toNormalizedOptions(args) {\n  const firstArg = args[0];\n\n  if (firstArg == null || 'kind' in firstArg || 'length' in firstArg) {\n    return {\n      nodes: firstArg,\n      source: args[1],\n      positions: args[2],\n      path: args[3],\n      originalError: args[4],\n      extensions: args[5],\n    };\n  }\n\n  return firstArg;\n}\n/**\n * A GraphQLError describes an Error found during the parse, validate, or\n * execute phases of performing a GraphQL operation. In addition to a message\n * and stack trace, it also includes information about the locations in a\n * GraphQL document and/or execution result that correspond to the Error.\n */\n\nclass GraphQLError extends Error {\n  /**\n   * An array of `{ line, column }` locations within the source GraphQL document\n   * which correspond to this error.\n   *\n   * Errors during validation often contain multiple locations, for example to\n   * point out two things with the same name. Errors during execution include a\n   * single location, the field which produced the error.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array describing the JSON-path into the execution response which\n   * corresponds to this error. Only included for errors during execution.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array of GraphQL AST Nodes corresponding to this error.\n   */\n\n  /**\n   * The source GraphQL document for the first location of this error.\n   *\n   * Note that if this Error represents more than one node, the source may not\n   * represent nodes after the first node.\n   */\n\n  /**\n   * An array of character offsets within the source GraphQL document\n   * which correspond to this error.\n   */\n\n  /**\n   * The original error thrown from a field resolver during execution.\n   */\n\n  /**\n   * Extension fields to add to the formatted error.\n   */\n\n  /**\n   * @deprecated Please use the `GraphQLErrorOptions` constructor overload instead.\n   */\n  constructor(message, ...rawArgs) {\n    var _this$nodes, _nodeLocations$, _ref;\n\n    const { nodes, source, positions, path, originalError, extensions } =\n      toNormalizedOptions(rawArgs);\n    super(message);\n    this.name = 'GraphQLError';\n    this.path = path !== null && path !== void 0 ? path : undefined;\n    this.originalError =\n      originalError !== null && originalError !== void 0\n        ? originalError\n        : undefined; // Compute list of blame nodes.\n\n    this.nodes = undefinedIfEmpty(\n      Array.isArray(nodes) ? nodes : nodes ? [nodes] : undefined,\n    );\n    const nodeLocations = undefinedIfEmpty(\n      (_this$nodes = this.nodes) === null || _this$nodes === void 0\n        ? void 0\n        : _this$nodes.map((node) => node.loc).filter((loc) => loc != null),\n    ); // Compute locations in the source for the given nodes/positions.\n\n    this.source =\n      source !== null && source !== void 0\n        ? source\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : (_nodeLocations$ = nodeLocations[0]) === null ||\n          _nodeLocations$ === void 0\n        ? void 0\n        : _nodeLocations$.source;\n    this.positions =\n      positions !== null && positions !== void 0\n        ? positions\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : nodeLocations.map((loc) => loc.start);\n    this.locations =\n      positions && source\n        ? positions.map((pos) => (0,_language_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(source, pos))\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : nodeLocations.map((loc) => (0,_language_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(loc.source, loc.start));\n    const originalExtensions = (0,_jsutils_isObjectLike_mjs__WEBPACK_IMPORTED_MODULE_1__.isObjectLike)(\n      originalError === null || originalError === void 0\n        ? void 0\n        : originalError.extensions,\n    )\n      ? originalError === null || originalError === void 0\n        ? void 0\n        : originalError.extensions\n      : undefined;\n    this.extensions =\n      (_ref =\n        extensions !== null && extensions !== void 0\n          ? extensions\n          : originalExtensions) !== null && _ref !== void 0\n        ? _ref\n        : Object.create(null); // Only properties prescribed by the spec should be enumerable.\n    // Keep the rest as non-enumerable.\n\n    Object.defineProperties(this, {\n      message: {\n        writable: true,\n        enumerable: true,\n      },\n      name: {\n        enumerable: false,\n      },\n      nodes: {\n        enumerable: false,\n      },\n      source: {\n        enumerable: false,\n      },\n      positions: {\n        enumerable: false,\n      },\n      originalError: {\n        enumerable: false,\n      },\n    }); // Include (non-enumerable) stack trace.\n\n    /* c8 ignore start */\n    // FIXME: https://github.com/graphql/graphql-js/issues/2317\n\n    if (\n      originalError !== null &&\n      originalError !== void 0 &&\n      originalError.stack\n    ) {\n      Object.defineProperty(this, 'stack', {\n        value: originalError.stack,\n        writable: true,\n        configurable: true,\n      });\n    } else if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, GraphQLError);\n    } else {\n      Object.defineProperty(this, 'stack', {\n        value: Error().stack,\n        writable: true,\n        configurable: true,\n      });\n    }\n    /* c8 ignore stop */\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'GraphQLError';\n  }\n\n  toString() {\n    let output = this.message;\n\n    if (this.nodes) {\n      for (const node of this.nodes) {\n        if (node.loc) {\n          output += '\\n\\n' + (0,_language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__.printLocation)(node.loc);\n        }\n      }\n    } else if (this.source && this.locations) {\n      for (const location of this.locations) {\n        output += '\\n\\n' + (0,_language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__.printSourceLocation)(this.source, location);\n      }\n    }\n\n    return output;\n  }\n\n  toJSON() {\n    const formattedError = {\n      message: this.message,\n    };\n\n    if (this.locations != null) {\n      formattedError.locations = this.locations;\n    }\n\n    if (this.path != null) {\n      formattedError.path = this.path;\n    }\n\n    if (this.extensions != null && Object.keys(this.extensions).length > 0) {\n      formattedError.extensions = this.extensions;\n    }\n\n    return formattedError;\n  }\n}\n\nfunction undefinedIfEmpty(array) {\n  return array === undefined || array.length === 0 ? undefined : array;\n}\n/**\n * See: https://spec.graphql.org/draft/#sec-Errors\n */\n\n/**\n * Prints a GraphQLError to a string, representing useful location information\n * about the error's position in the source.\n *\n * @deprecated Please use `error.toString` instead. Will be removed in v17\n */\nfunction printError(error) {\n  return error.toString();\n}\n/**\n * Given a GraphQLError, format it according to the rules described by the\n * Response Format, Errors section of the GraphQL Specification.\n *\n * @deprecated Please use `error.toJSON` instead. Will be removed in v17\n */\n\nfunction formatError(error) {\n  return error.toJSON();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/error/GraphQLError.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql/error/syntaxError.mjs":
/*!****************************************************!*\
  !*** ./node_modules/graphql/error/syntaxError.mjs ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   syntaxError: function() { return /* binding */ syntaxError; }\n/* harmony export */ });\n/* harmony import */ var _GraphQLError_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GraphQLError.mjs */ \"(app-pages-browser)/./node_modules/graphql/error/GraphQLError.mjs\");\n\n/**\n * Produces a GraphQLError representing a syntax error, containing useful\n * descriptive information about the syntax error's position in the source.\n */\n\nfunction syntaxError(source, position, description) {\n  return new _GraphQLError_mjs__WEBPACK_IMPORTED_MODULE_0__.GraphQLError(`Syntax Error: ${description}`, {\n    source,\n    positions: [position],\n  });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsL2Vycm9yL3N5bnRheEVycm9yLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRDtBQUNsRDtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQLGFBQWEsMkRBQVksa0JBQWtCLFlBQVk7QUFDdkQ7QUFDQTtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9lcnJvci9zeW50YXhFcnJvci5tanM/NWM0NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBHcmFwaFFMRXJyb3IgfSBmcm9tICcuL0dyYXBoUUxFcnJvci5tanMnO1xuLyoqXG4gKiBQcm9kdWNlcyBhIEdyYXBoUUxFcnJvciByZXByZXNlbnRpbmcgYSBzeW50YXggZXJyb3IsIGNvbnRhaW5pbmcgdXNlZnVsXG4gKiBkZXNjcmlwdGl2ZSBpbmZvcm1hdGlvbiBhYm91dCB0aGUgc3ludGF4IGVycm9yJ3MgcG9zaXRpb24gaW4gdGhlIHNvdXJjZS5cbiAqL1xuXG5leHBvcnQgZnVuY3Rpb24gc3ludGF4RXJyb3Ioc291cmNlLCBwb3NpdGlvbiwgZGVzY3JpcHRpb24pIHtcbiAgcmV0dXJuIG5ldyBHcmFwaFFMRXJyb3IoYFN5bnRheCBFcnJvcjogJHtkZXNjcmlwdGlvbn1gLCB7XG4gICAgc291cmNlLFxuICAgIHBvc2l0aW9uczogW3Bvc2l0aW9uXSxcbiAgfSk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/error/syntaxError.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql/jsutils/devAssert.mjs":
/*!****************************************************!*\
  !*** ./node_modules/graphql/jsutils/devAssert.mjs ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   devAssert: function() { return /* binding */ devAssert; }\n/* harmony export */ });\nfunction devAssert(condition, message) {\n  const booleanCondition = Boolean(condition);\n\n  if (!booleanCondition) {\n    throw new Error(message);\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsL2pzdXRpbHMvZGV2QXNzZXJ0Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9qc3V0aWxzL2RldkFzc2VydC5tanM/MmUxMiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZGV2QXNzZXJ0KGNvbmRpdGlvbiwgbWVzc2FnZSkge1xuICBjb25zdCBib29sZWFuQ29uZGl0aW9uID0gQm9vbGVhbihjb25kaXRpb24pO1xuXG4gIGlmICghYm9vbGVhbkNvbmRpdGlvbikge1xuICAgIHRocm93IG5ldyBFcnJvcihtZXNzYWdlKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/jsutils/devAssert.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql/jsutils/inspect.mjs":
/*!**************************************************!*\
  !*** ./node_modules/graphql/jsutils/inspect.mjs ***!
  \**************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inspect: function() { return /* binding */ inspect; }\n/* harmony export */ });\nconst MAX_ARRAY_LENGTH = 10;\nconst MAX_RECURSIVE_DEPTH = 2;\n/**\n * Used to print values in error messages.\n */\n\nfunction inspect(value) {\n  return formatValue(value, []);\n}\n\nfunction formatValue(value, seenValues) {\n  switch (typeof value) {\n    case 'string':\n      return JSON.stringify(value);\n\n    case 'function':\n      return value.name ? `[function ${value.name}]` : '[function]';\n\n    case 'object':\n      return formatObjectValue(value, seenValues);\n\n    default:\n      return String(value);\n  }\n}\n\nfunction formatObjectValue(value, previouslySeenValues) {\n  if (value === null) {\n    return 'null';\n  }\n\n  if (previouslySeenValues.includes(value)) {\n    return '[Circular]';\n  }\n\n  const seenValues = [...previouslySeenValues, value];\n\n  if (isJSONable(value)) {\n    const jsonValue = value.toJSON(); // check for infinite recursion\n\n    if (jsonValue !== value) {\n      return typeof jsonValue === 'string'\n        ? jsonValue\n        : formatValue(jsonValue, seenValues);\n    }\n  } else if (Array.isArray(value)) {\n    return formatArray(value, seenValues);\n  }\n\n  return formatObject(value, seenValues);\n}\n\nfunction isJSONable(value) {\n  return typeof value.toJSON === 'function';\n}\n\nfunction formatObject(object, seenValues) {\n  const entries = Object.entries(object);\n\n  if (entries.length === 0) {\n    return '{}';\n  }\n\n  if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n    return '[' + getObjectTag(object) + ']';\n  }\n\n  const properties = entries.map(\n    ([key, value]) => key + ': ' + formatValue(value, seenValues),\n  );\n  return '{ ' + properties.join(', ') + ' }';\n}\n\nfunction formatArray(array, seenValues) {\n  if (array.length === 0) {\n    return '[]';\n  }\n\n  if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n    return '[Array]';\n  }\n\n  const len = Math.min(MAX_ARRAY_LENGTH, array.length);\n  const remaining = array.length - len;\n  const items = [];\n\n  for (let i = 0; i < len; ++i) {\n    items.push(formatValue(array[i], seenValues));\n  }\n\n  if (remaining === 1) {\n    items.push('... 1 more item');\n  } else if (remaining > 1) {\n    items.push(`... ${remaining} more items`);\n  }\n\n  return '[' + items.join(', ') + ']';\n}\n\nfunction getObjectTag(object) {\n  const tag = Object.prototype.toString\n    .call(object)\n    .replace(/^\\[object /, '')\n    .replace(/]$/, '');\n\n  if (tag === 'Object' && typeof object.constructor === 'function') {\n    const name = object.constructor.name;\n\n    if (typeof name === 'string' && name !== '') {\n      return name;\n    }\n  }\n\n  return tag;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/jsutils/inspect.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql/jsutils/instanceOf.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/graphql/jsutils/instanceOf.mjs ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   instanceOf: function() { return /* binding */ instanceOf; }\n/* harmony export */ });\n/* harmony import */ var _inspect_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./inspect.mjs */ \"(app-pages-browser)/./node_modules/graphql/jsutils/inspect.mjs\");\n\n/* c8 ignore next 3 */\n\nconst isProduction =\n  globalThis.process && // eslint-disable-next-line no-undef\n  \"development\" === 'production';\n/**\n * A replacement for instanceof which includes an error warning when multi-realm\n * constructors are detected.\n * See: https://expressjs.com/en/advanced/best-practice-performance.html#set-node_env-to-production\n * See: https://webpack.js.org/guides/production/\n */\n\nconst instanceOf =\n  /* c8 ignore next 6 */\n  // FIXME: https://github.com/graphql/graphql-js/issues/2317\n  isProduction\n    ? function instanceOf(value, constructor) {\n        return value instanceof constructor;\n      }\n    : function instanceOf(value, constructor) {\n        if (value instanceof constructor) {\n          return true;\n        }\n\n        if (typeof value === 'object' && value !== null) {\n          var _value$constructor;\n\n          // Prefer Symbol.toStringTag since it is immune to minification.\n          const className = constructor.prototype[Symbol.toStringTag];\n          const valueClassName = // We still need to support constructor's name to detect conflicts with older versions of this library.\n            Symbol.toStringTag in value // @ts-expect-error TS bug see, https://github.com/microsoft/TypeScript/issues/38009\n              ? value[Symbol.toStringTag]\n              : (_value$constructor = value.constructor) === null ||\n                _value$constructor === void 0\n              ? void 0\n              : _value$constructor.name;\n\n          if (className === valueClassName) {\n            const stringifiedValue = (0,_inspect_mjs__WEBPACK_IMPORTED_MODULE_0__.inspect)(value);\n            throw new Error(`Cannot use ${className} \"${stringifiedValue}\" from another module or realm.\n\nEnsure that there is only one instance of \"graphql\" in the node_modules\ndirectory. If different versions of \"graphql\" are the dependencies of other\nrelied on modules, use \"resolutions\" to ensure only one version is installed.\n\nhttps://yarnpkg.com/en/docs/selective-version-resolutions\n\nDuplicate \"graphql\" modules cannot be used at the same time since different\nversions may have different capabilities and behavior. The data from one\nversion used in the function from another could produce confusing and\nspurious results.`);\n          }\n        }\n\n        return false;\n      };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/jsutils/instanceOf.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql/jsutils/invariant.mjs":
/*!****************************************************!*\
  !*** ./node_modules/graphql/jsutils/invariant.mjs ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invariant: function() { return /* binding */ invariant; }\n/* harmony export */ });\nfunction invariant(condition, message) {\n  const booleanCondition = Boolean(condition);\n\n  if (!booleanCondition) {\n    throw new Error(\n      message != null ? message : 'Unexpected invariant triggered.',\n    );\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsL2pzdXRpbHMvaW52YXJpYW50Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2dyYXBocWwvanN1dGlscy9pbnZhcmlhbnQubWpzPzY4NzAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGludmFyaWFudChjb25kaXRpb24sIG1lc3NhZ2UpIHtcbiAgY29uc3QgYm9vbGVhbkNvbmRpdGlvbiA9IEJvb2xlYW4oY29uZGl0aW9uKTtcblxuICBpZiAoIWJvb2xlYW5Db25kaXRpb24pIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICBtZXNzYWdlICE9IG51bGwgPyBtZXNzYWdlIDogJ1VuZXhwZWN0ZWQgaW52YXJpYW50IHRyaWdnZXJlZC4nLFxuICAgICk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/jsutils/invariant.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/graphql/jsutils/isObjectLike.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/graphql/jsutils/isObjectLike.mjs ***!
  \*******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObjectLike: function() { return /* binding */ isObjectLike; }\n/* harmony export */ });\n/**\n * Return true if `value` is object-like. A value is object-like if it's not\n * `null` and has a `typeof` result of \"object\".\n */\nfunction isObjectLike(value) {\n  return typeof value == 'object' && value !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9ncmFwaHFsL2pzdXRpbHMvaXNPYmplY3RMaWtlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2dyYXBocWwvanN1dGlscy9pc09iamVjdExpa2UubWpzPzJlMjAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZXR1cm4gdHJ1ZSBpZiBgdmFsdWVgIGlzIG9iamVjdC1saWtlLiBBIHZhbHVlIGlzIG9iamVjdC1saWtlIGlmIGl0J3Mgbm90XG4gKiBgbnVsbGAgYW5kIGhhcyBhIGB0eXBlb2ZgIHJlc3VsdCBvZiBcIm9iamVjdFwiLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaXNPYmplY3RMaWtlKHZhbHVlKSB7XG4gIHJldHVybiB0eXBlb2YgdmFsdWUgPT0gJ29iamVjdCcgJiYgdmFsdWUgIT09IG51bGw7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/graphql/jsutils/isObjectLike.mjs\n"));

/***/ })

}]);
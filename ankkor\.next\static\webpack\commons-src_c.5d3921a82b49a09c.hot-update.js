"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("commons-src_c",{

/***/ "(app-pages-browser)/./src/components/ui/loader.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/loader.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Loader = (param)=>{\n    let { size = \"md\", color = \"#2c2c27\", className = \"\" } = param;\n    // Size mappings\n    const sizeMap = {\n        sm: {\n            container: \"w-6 h-6\",\n            dot: \"w-1 h-1\"\n        },\n        md: {\n            container: \"w-10 h-10\",\n            dot: \"w-1.5 h-1.5\"\n        },\n        lg: {\n            container: \"w-16 h-16\",\n            dot: \"w-2 h-2\"\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-cba83ab4e8da42d9\" + \" \" + \"flex items-center justify-center \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-cba83ab4e8da42d9\" + \" \" + \"relative \".concat(sizeMap[size].container),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: color,\n                            animation: \"loaderDot1 1.5s infinite\"\n                        },\n                        className: \"jsx-cba83ab4e8da42d9\" + \" \" + \"absolute top-0 left-1/2 -translate-x-1/2 \".concat(sizeMap[size].dot, \" rounded-full\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: color,\n                            animation: \"loaderDot2 1.5s infinite\"\n                        },\n                        className: \"jsx-cba83ab4e8da42d9\" + \" \" + \"absolute top-1/2 right-0 -translate-y-1/2 \".concat(sizeMap[size].dot, \" rounded-full\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: color,\n                            animation: \"loaderDot3 1.5s infinite\"\n                        },\n                        className: \"jsx-cba83ab4e8da42d9\" + \" \" + \"absolute bottom-0 left-1/2 -translate-x-1/2 \".concat(sizeMap[size].dot, \" rounded-full\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: color,\n                            animation: \"loaderDot4 1.5s infinite\"\n                        },\n                        className: \"jsx-cba83ab4e8da42d9\" + \" \" + \"absolute top-1/2 left-0 -translate-y-1/2 \".concat(sizeMap[size].dot, \" rounded-full\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            border: \"2px solid \".concat(color),\n                            borderTopColor: \"transparent\",\n                            animation: \"loaderRotate 1s linear infinite\"\n                        },\n                        className: \"jsx-cba83ab4e8da42d9\" + \" \" + \"absolute inset-0 rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"cba83ab4e8da42d9\",\n                children: \"@-webkit-keyframes loaderRotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes loaderRotate{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes loaderRotate{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes loaderRotate{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@-moz-keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@-o-keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@keyframes loaderDot1{0%,100%{opacity:.2}25%{opacity:1}}@-webkit-keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@-moz-keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@-o-keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@keyframes loaderDot2{0%,100%{opacity:.2}50%{opacity:1}}@-webkit-keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@-moz-keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@-o-keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@keyframes loaderDot3{0%,100%{opacity:.2}75%{opacity:1}}@-webkit-keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}@-moz-keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}@-o-keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}@keyframes loaderDot4{0%,100%{opacity:1}50%{opacity:.2}}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\loader.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Loader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Loader);\nvar _c;\n$RefreshReg$(_c, \"Loader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/loader.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page-src_lib_i"],{

/***/ "(app-pages-browser)/./src/lib/inventoryMapping.ts":
/*!*************************************!*\
  !*** ./src/lib/inventoryMapping.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addInventoryMapping: function() { return /* binding */ addInventoryMapping; },\n/* harmony export */   clearInventoryMappings: function() { return /* binding */ clearInventoryMappings; },\n/* harmony export */   getAllInventoryMappings: function() { return /* binding */ getAllInventoryMappings; },\n/* harmony export */   getProductHandleFromInventory: function() { return /* binding */ getProductHandleFromInventory; },\n/* harmony export */   loadInventoryMap: function() { return /* binding */ loadInventoryMap; },\n/* harmony export */   saveInventoryMap: function() { return /* binding */ saveInventoryMap; },\n/* harmony export */   updateInventoryMappings: function() { return /* binding */ updateInventoryMappings; }\n/* harmony export */ });\n/* harmony import */ var _upstash_redis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @upstash/redis */ \"(app-pages-browser)/./node_modules/@upstash/redis/nodejs.mjs\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n// Redis key prefix for inventory mappings\nconst KEY_PREFIX = \"inventory:mapping:\";\n// Initialize Redis client with support for both Upstash Redis and Vercel KV variables\nconst redis = new _upstash_redis__WEBPACK_IMPORTED_MODULE_0__.Redis({\n    url: process.env.UPSTASH_REDIS_REST_URL || process.env.NEXT_PUBLIC_KV_REST_API_URL || \"\",\n    token: process.env.UPSTASH_REDIS_REST_TOKEN || process.env.NEXT_PUBLIC_KV_REST_API_TOKEN || \"\"\n});\n// In-memory fallback for local development or when Redis is unavailable\nconst memoryStorage = {};\n/**\r\n * Check if Redis is available\r\n */ function isRedisAvailable() {\n    return Boolean(process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN || process.env.NEXT_PUBLIC_KV_REST_API_URL && process.env.NEXT_PUBLIC_KV_REST_API_TOKEN);\n}\n/**\r\n * Load inventory mapping from storage\r\n * Maps inventory_item_ids to product handles\r\n * \r\n * @returns A record mapping inventory_item_ids to product handles\r\n */ async function loadInventoryMap() {\n    // Use Redis if available\n    if (isRedisAvailable()) {\n        try {\n            // Get all keys with our prefix\n            const keys = await redis.keys(\"\".concat(KEY_PREFIX, \"*\"));\n            if (keys.length === 0) {\n                console.log(\"No existing inventory mappings found in Redis\");\n                return {};\n            }\n            // Create a mapping object\n            const map = {};\n            // Get all values in a single batch operation\n            const values = await redis.mget(...keys);\n            // Populate the mapping object\n            keys.forEach((key, index)=>{\n                const inventoryItemId = key.replace(KEY_PREFIX, \"\");\n                const productHandle = values[index];\n                map[inventoryItemId] = productHandle;\n            });\n            console.log(\"Loaded inventory mapping with \".concat(Object.keys(map).length, \" entries from Redis\"));\n            return map;\n        } catch (error) {\n            console.error(\"Error loading inventory mapping from Redis:\", error);\n            console.log(\"Falling back to in-memory storage\");\n            return {\n                ...memoryStorage\n            };\n        }\n    } else {\n        // Fallback to in-memory when Redis is not available\n        return {\n            ...memoryStorage\n        };\n    }\n}\n/**\r\n * Save inventory mapping to storage\r\n * \r\n * @param map The inventory mapping to save\r\n */ async function saveInventoryMap(map) {\n    // Use Redis if available\n    if (isRedisAvailable()) {\n        try {\n            // Convert map to array of Redis commands\n            const pipeline = redis.pipeline();\n            // First clear existing keys with this prefix\n            const existingKeys = await redis.keys(\"\".concat(KEY_PREFIX, \"*\"));\n            if (existingKeys.length > 0) {\n                pipeline.del(...existingKeys);\n            }\n            // Set new key-value pairs\n            Object.entries(map).forEach((param)=>{\n                let [inventoryItemId, productHandle] = param;\n                pipeline.set(\"\".concat(KEY_PREFIX).concat(inventoryItemId), productHandle);\n            });\n            // Execute all commands in a single transaction\n            await pipeline.exec();\n            console.log(\"Saved inventory mapping with \".concat(Object.keys(map).length, \" entries to Redis\"));\n        } catch (error) {\n            console.error(\"Error saving inventory mapping to Redis:\", error);\n            console.log(\"Falling back to in-memory storage\");\n            // Update in-memory storage as fallback\n            Object.assign(memoryStorage, map);\n        }\n    } else {\n        // Fallback to in-memory when Redis is not available\n        Object.assign(memoryStorage, map);\n        console.log(\"Saved inventory mapping with \".concat(Object.keys(map).length, \" entries to memory\"));\n    }\n}\n/**\r\n * Add a mapping between an inventory_item_id and a product handle\r\n * \r\n * @param inventoryItemId The Shopify inventory_item_id\r\n * @param productHandle The product handle\r\n * @returns True if the mapping was added or updated, false if there was an error\r\n */ async function addInventoryMapping(inventoryItemId, productHandle) {\n    try {\n        if (isRedisAvailable()) {\n            await redis.set(\"\".concat(KEY_PREFIX).concat(inventoryItemId), productHandle);\n            console.log(\"Added mapping to Redis: \".concat(inventoryItemId, \" -> \").concat(productHandle));\n        } else {\n            memoryStorage[inventoryItemId] = productHandle;\n            console.log(\"Added mapping to memory: \".concat(inventoryItemId, \" -> \").concat(productHandle));\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Error adding inventory mapping:\", error);\n        // Try memory as fallback\n        try {\n            memoryStorage[inventoryItemId] = productHandle;\n            console.log(\"Added mapping to memory fallback: \".concat(inventoryItemId, \" -> \").concat(productHandle));\n            return true;\n        } catch (memError) {\n            console.error(\"Error adding to memory fallback:\", memError);\n            return false;\n        }\n    }\n}\n/**\r\n * Get the product handle associated with an inventory_item_id\r\n * \r\n * @param inventoryItemId The Shopify inventory_item_id\r\n * @returns The product handle, or null if not found\r\n */ async function getProductHandleFromInventory(inventoryItemId) {\n    try {\n        if (isRedisAvailable()) {\n            const handle = await redis.get(\"\".concat(KEY_PREFIX).concat(inventoryItemId));\n            return handle || null;\n        } else {\n            return memoryStorage[inventoryItemId] || null;\n        }\n    } catch (error) {\n        console.error(\"Error getting product handle from Redis:\", error);\n        // Try memory as fallback\n        try {\n            return memoryStorage[inventoryItemId] || null;\n        } catch (memError) {\n            console.error(\"Error getting from memory fallback:\", memError);\n            return null;\n        }\n    }\n}\n/**\r\n * Batch update multiple inventory mappings\r\n * \r\n * @param mappings An array of inventory_item_id to product handle mappings\r\n * @returns True if all mappings were successfully updated, false otherwise\r\n */ async function updateInventoryMappings(mappings) {\n    try {\n        if (isRedisAvailable()) {\n            const pipeline = redis.pipeline();\n            for (const { inventoryItemId, productHandle } of mappings){\n                pipeline.set(\"\".concat(KEY_PREFIX).concat(inventoryItemId), productHandle);\n            }\n            await pipeline.exec();\n            console.log(\"Updated \".concat(mappings.length, \" inventory mappings in Redis\"));\n        } else {\n            for (const { inventoryItemId, productHandle } of mappings){\n                memoryStorage[inventoryItemId] = productHandle;\n            }\n            console.log(\"Updated \".concat(mappings.length, \" inventory mappings in memory\"));\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Error updating inventory mappings in Redis:\", error);\n        // Try memory as fallback\n        try {\n            for (const { inventoryItemId, productHandle } of mappings){\n                memoryStorage[inventoryItemId] = productHandle;\n            }\n            console.log(\"Updated \".concat(mappings.length, \" inventory mappings in memory fallback\"));\n            return true;\n        } catch (memError) {\n            console.error(\"Error updating in memory fallback:\", memError);\n            return false;\n        }\n    }\n}\n/**\r\n * Get all inventory mappings\r\n * \r\n * @returns The complete inventory mapping\r\n */ async function getAllInventoryMappings() {\n    return await loadInventoryMap();\n}\n/**\r\n * Clear all inventory mappings (use with caution)\r\n * \r\n * @returns True if the mappings were successfully cleared, false otherwise\r\n */ async function clearInventoryMappings() {\n    try {\n        if (isRedisAvailable()) {\n            const keys = await redis.keys(\"\".concat(KEY_PREFIX, \"*\"));\n            if (keys.length > 0) {\n                await redis.del(...keys);\n            }\n            console.log(\"Cleared all inventory mappings from Redis\");\n        } else {\n            // Clear in-memory storage\n            Object.keys(memoryStorage).forEach((key)=>{\n                delete memoryStorage[key];\n            });\n            console.log(\"Cleared all inventory mappings from memory\");\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Error clearing inventory mappings:\", error);\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/inventoryMapping.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/localCartStore.ts":
/*!***********************************!*\
  !*** ./src/lib/localCartStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   useLocalCartItems: function() { return /* binding */ useLocalCartItems; },\n/* harmony export */   useLocalCartStore: function() { return /* binding */ useLocalCartStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/**\n * Local Cart Store for Ankkor E-commerce\n *\n * This implementation uses local storage to persist cart data on the client side.\n * When the user proceeds to checkout, the cart items are sent to WooCommerce\n * to create a server-side cart before redirecting to the checkout page.\n *\n * This approach allows for a faster, more responsive cart experience while\n * still integrating with WooCommerce's checkout process.\n */ /* __next_internal_client_entry_do_not_use__ useLocalCartStore,useLocalCartItems,formatPrice auto */ \n\n// Local storage version to handle migrations\nconst STORAGE_VERSION = 1;\n// Generate a unique ID for cart items\nconst generateItemId = ()=>{\n    return Math.random().toString(36).substring(2, 15);\n};\n// Create the store\nconst useLocalCartStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // State\n        items: [],\n        itemCount: 0,\n        isLoading: false,\n        error: null,\n        // Actions\n        addToCart: async (item)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const items = get().items;\n                // Check if the item already exists in the cart\n                const existingItemIndex = items.findIndex((cartItem)=>cartItem.productId === item.productId && cartItem.variationId === item.variationId);\n                if (existingItemIndex !== -1) {\n                    // If item exists, update quantity\n                    const updatedItems = [\n                        ...items\n                    ];\n                    updatedItems[existingItemIndex].quantity += item.quantity;\n                    set({\n                        items: updatedItems,\n                        itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                        isLoading: false\n                    });\n                } else {\n                    // If item doesn't exist, add it with a new ID\n                    const newItem = {\n                        ...item,\n                        id: generateItemId()\n                    };\n                    set({\n                        items: [\n                            ...items,\n                            newItem\n                        ],\n                        itemCount: items.reduce((sum, item)=>sum + item.quantity, 0) + newItem.quantity,\n                        isLoading: false\n                    });\n                }\n                // Show success message\n                console.log(\"Item added to cart successfully\");\n            } catch (error) {\n                console.error(\"Error adding item to cart:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        updateCartItem: (id, quantity)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const items = get().items;\n                if (quantity <= 0) {\n                    // If quantity is 0 or negative, remove the item\n                    return get().removeCartItem(id);\n                }\n                // Find the item and update its quantity\n                const updatedItems = items.map((item)=>item.id === id ? {\n                        ...item,\n                        quantity\n                    } : item);\n                set({\n                    items: updatedItems,\n                    itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"Error updating cart item:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        removeCartItem: (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const items = get().items;\n                const updatedItems = items.filter((item)=>item.id !== id);\n                set({\n                    items: updatedItems,\n                    itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"Error removing cart item:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        clearCart: ()=>{\n            set({\n                items: [],\n                itemCount: 0,\n                isLoading: false,\n                error: null\n            });\n        },\n        setError: (error)=>{\n            set({\n                error\n            });\n        },\n        setIsLoading: (isLoading)=>{\n            set({\n                isLoading\n            });\n        },\n        // Helper methods\n        subtotal: ()=>{\n            const items = get().items;\n            return items.reduce((total, item)=>{\n                return total + parseFloat(item.price) * item.quantity;\n            }, 0);\n        },\n        total: ()=>{\n            // For now, total is the same as subtotal\n            // In the future, you could add shipping, tax, etc.\n            return get().subtotal();\n        },\n        // Sync cart with WooCommerce and get checkout URL\n        syncWithWooCommerce: async ()=>{\n            const items = get().items;\n            if (items.length === 0) {\n                throw new Error(\"Cart is empty\");\n            }\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const baseUrl = \"https://deepskyblue-penguin-370791.hostingersite.com\" || 0;\n                if (!baseUrl) {\n                    throw new Error(\"WooCommerce URL not configured\");\n                }\n                // For single item, use direct add-to-cart URL with checkout redirect\n                if (items.length === 1) {\n                    const item = items[0];\n                    // Create a URL that will:\n                    // 1. Add the item to cart\n                    // 2. Redirect to checkout\n                    // 3. Enable guest checkout\n                    // 4. Skip login reminder\n                    let checkoutUrl = \"\".concat(baseUrl, \"/?wc-ajax=add_to_cart\");\n                    checkoutUrl += \"&product_id=\".concat(item.productId);\n                    checkoutUrl += \"&quantity=\".concat(item.quantity);\n                    if (item.variationId) {\n                        checkoutUrl += \"&variation_id=\".concat(item.variationId);\n                    }\n                    // Add redirect to checkout parameter\n                    checkoutUrl += \"&redirect_to=\".concat(encodeURIComponent(\"\".concat(baseUrl, \"/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0\")));\n                    set({\n                        isLoading: false\n                    });\n                    return checkoutUrl;\n                }\n                // For multiple items, we need to create a proper checkout URL\n                // This approach uses the direct checkout URL with guest checkout parameters\n                const checkoutUrl = \"\".concat(baseUrl, \"/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0\");\n                // In a production environment, you should use the WooCommerce REST API\n                // to properly create a cart with multiple items\n                console.warn(\"Multiple items in cart. Using direct checkout URL. For a complete solution, implement WooCommerce REST API for full cart sync.\");\n                set({\n                    isLoading: false\n                });\n                return checkoutUrl;\n            } catch (error) {\n                console.error(\"Error syncing with WooCommerce:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n                return null;\n            }\n        }\n    }), {\n    name: \"ankkor-local-cart\",\n    version: STORAGE_VERSION\n}));\n// Helper hooks\nconst useLocalCartItems = ()=>useLocalCartStore((state)=>state.items);\n// Helper functions\nconst formatPrice = function(price) {\n    let currencyCode = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"INR\";\n    const amount = typeof price === \"string\" ? parseFloat(price) : price;\n    return new Intl.NumberFormat(\"en-IN\", {\n        style: \"currency\",\n        currency: currencyCode,\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(amount);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/localCartStore.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/productUtils.ts":
/*!*********************************!*\
  !*** ./src/lib/productUtils.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   getCurrencySymbol: function() { return /* binding */ getCurrencySymbol; },\n/* harmony export */   getVariantInventoryInfo: function() { return /* binding */ getVariantInventoryInfo; },\n/* harmony export */   hasLowInventory: function() { return /* binding */ hasLowInventory; },\n/* harmony export */   processProductInventoryMappings: function() { return /* binding */ processProductInventoryMappings; }\n/* harmony export */ });\n/* harmony import */ var _lib_inventoryMapping__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/inventoryMapping */ \"(app-pages-browser)/./src/lib/inventoryMapping.ts\");\n\n/**\r\n * Utility functions for working with product data\r\n */ /**\r\n * Process product data to set up inventory-to-product mappings\r\n * This helps with more efficient inventory updates via webhooks\r\n * @param product The product data from Shopify\r\n */ function processProductInventoryMappings(product) {\n    if (!product) return;\n    try {\n        // Check product structure\n        if (!product.variants) return;\n        // Create batch for more efficient updating of Redis\n        const mappings = [];\n        // Handle different API response formats\n        if (Array.isArray(product.variants)) {\n            // Direct array of variants\n            product.variants.forEach((variant)=>{\n                if (variant.inventory_item_id) {\n                    const inventoryItemId = variant.inventory_item_id.toString();\n                    // Update in-memory cache immediately for faster access\n                    (0,_lib_inventoryMapping__WEBPACK_IMPORTED_MODULE_0__.addInventoryMapping)(inventoryItemId, product.handle);\n                    // Add to batch for Redis\n                    mappings.push({\n                        inventoryItemId,\n                        productHandle: product.handle\n                    });\n                }\n            });\n        } else if (product.variants.edges) {\n            // GraphQL edge format\n            product.variants.edges.forEach((edge)=>{\n                var _edge_node;\n                if ((_edge_node = edge.node) === null || _edge_node === void 0 ? void 0 : _edge_node.inventory_item_id) {\n                    const inventoryItemId = edge.node.inventory_item_id.toString();\n                    // Update in-memory cache immediately for faster access\n                    (0,_lib_inventoryMapping__WEBPACK_IMPORTED_MODULE_0__.addInventoryMapping)(inventoryItemId, product.handle);\n                    // Add to batch for Redis\n                    mappings.push({\n                        inventoryItemId,\n                        productHandle: product.handle\n                    });\n                }\n            });\n        } else if (product.variants.nodes) {\n            // Modern GraphQL node format\n            product.variants.nodes.forEach((node)=>{\n                if (node === null || node === void 0 ? void 0 : node.inventory_item_id) {\n                    const inventoryItemId = node.inventory_item_id.toString();\n                    // Update in-memory cache immediately for faster access\n                    (0,_lib_inventoryMapping__WEBPACK_IMPORTED_MODULE_0__.addInventoryMapping)(inventoryItemId, product.handle);\n                    // Add to batch for Redis\n                    mappings.push({\n                        inventoryItemId,\n                        productHandle: product.handle\n                    });\n                }\n            });\n        }\n        // If we have inventory items to map, batch update them in persistent storage\n        if (mappings.length > 0) {\n            // We don't await this - it's a background operation\n            Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/inventoryMapping */ \"(app-pages-browser)/./src/lib/inventoryMapping.ts\")).then((param)=>{\n                let { updateInventoryMappings } = param;\n                updateInventoryMappings(mappings).catch((error)=>{\n                    console.error(\"Error batch updating inventory mappings:\", error);\n                });\n            });\n        }\n        console.log(\"Processed \".concat(mappings.length, \" inventory mappings for product: \").concat(product.handle));\n    } catch (error) {\n        console.error(\"Error processing product inventory mappings:\", error);\n    }\n}\n/**\r\n * Extract inventory information from a product variant\r\n * @param variant The product variant\r\n */ function getVariantInventoryInfo(variant) {\n    if (!variant) return {\n        available: false,\n        quantity: 0\n    };\n    // Check if the variant is available for sale\n    const available = variant.availableForSale || false;\n    // Get the quantity available (may be null if not tracked)\n    let quantity = 0;\n    if (variant.quantityAvailable !== undefined && variant.quantityAvailable !== null) {\n        quantity = variant.quantityAvailable;\n    } else if (variant.inventoryQuantity !== undefined && variant.inventoryQuantity !== null) {\n        quantity = variant.inventoryQuantity;\n    }\n    return {\n        available,\n        quantity\n    };\n}\n/**\r\n * Determine if a product has low inventory\r\n * @param variant The product variant\r\n * @param threshold The threshold for low inventory (default: 5)\r\n */ function hasLowInventory(variant) {\n    let threshold = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5;\n    const { available, quantity } = getVariantInventoryInfo(variant);\n    return available && quantity > 0 && quantity <= threshold;\n}\n/**\r\n * Format a price for display\r\n * @param amount The price amount as a string or number\r\n * @param currencyCode The currency code (default: INR for Indian Rupees)\r\n */ function formatPrice(amount) {\n    let currencyCode = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"INR\";\n    if (!amount) return \"\";\n    // Convert string to number if needed\n    const price = typeof amount === \"string\" ? parseFloat(amount) : amount;\n    // Return formatted price based on currency\n    switch(currencyCode){\n        case \"INR\":\n            return \"₹\".concat(price.toFixed(2));\n        case \"USD\":\n            return \"$\".concat(price.toFixed(2));\n        case \"EUR\":\n            return \"€\".concat(price.toFixed(2));\n        case \"GBP\":\n            return \"\\xa3\".concat(price.toFixed(2));\n        default:\n            return \"\".concat(price.toFixed(2), \" \").concat(currencyCode);\n    }\n}\n/**\r\n * Get currency symbol from currency code\r\n * @param currencyCode The currency code\r\n * @returns The currency symbol\r\n */ function getCurrencySymbol() {\n    let currencyCode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"INR\";\n    switch(currencyCode){\n        case \"INR\":\n            return \"₹\";\n        case \"USD\":\n            return \"$\";\n        case \"EUR\":\n            return \"€\";\n        case \"GBP\":\n            return \"\\xa3\";\n        default:\n            return currencyCode;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/productUtils.ts\n"));

/***/ })

}]);
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page-src_components_p",{

/***/ "(app-pages-browser)/./src/components/providers/CustomerProvider.tsx":
/*!*******************************************************!*\
  !*** ./src/components/providers/CustomerProvider.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomerProvider: function() { return /* binding */ CustomerProvider; },\n/* harmony export */   useCustomer: function() { return /* binding */ useCustomer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_clientAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/clientAuth */ \"(app-pages-browser)/./src/lib/clientAuth.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/toast */ \"(app-pages-browser)/./src/components/ui/toast.tsx\");\n/* harmony import */ var _lib_wooStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/wooStore */ \"(app-pages-browser)/./src/lib/wooStore.ts\");\n/* harmony import */ var _hooks_useAuthCartSync__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useAuthCartSync */ \"(app-pages-browser)/./src/hooks/useAuthCartSync.ts\");\n/* __next_internal_client_entry_do_not_use__ useCustomer,CustomerProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Create the context\nconst CustomerContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    customer: null,\n    isLoading: true,\n    isAuthenticated: false,\n    login: async ()=>{},\n    register: async ()=>{},\n    logout: ()=>{},\n    updateProfile: async ()=>{},\n    error: null,\n    refreshCustomer: async ()=>{}\n});\n// Custom hook to use the customer context\nconst useCustomer = ()=>{\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CustomerContext);\n};\n_s(useCustomer, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n// Provider component\nfunction CustomerProvider(param) {\n    let { children } = param;\n    _s1();\n    const [customer, setCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { addToast } = (0,_components_ui_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    // Initialize auth cart sync\n    (0,_hooks_useAuthCartSync__WEBPACK_IMPORTED_MODULE_7__.useAuthCartSync)();\n    // Transform customer data to ensure it has all required fields\n    const transformCustomerData = (customerData)=>{\n        if (!customerData) return null;\n        return {\n            ...customerData,\n            displayName: customerData.displayName || customerData.username || \"\".concat(customerData.firstName || \"\", \" \").concat(customerData.lastName || \"\").trim() || \"User\"\n        };\n    };\n    // Check authentication and get customer data from API\n    const checkAuthAndGetCustomer = async ()=>{\n        try {\n            console.log(\"CustomerProvider: Checking authentication via /api/auth/me\");\n            const response = await fetch(\"/api/auth/me\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            console.log(\"CustomerProvider: Auth API response status:\", response.status);\n            const result = await response.json();\n            console.log(\"CustomerProvider: Auth API result:\", result);\n            if (result.success && result.customer) {\n                return {\n                    success: true,\n                    customer: result.customer\n                };\n            } else {\n                return {\n                    success: false,\n                    message: result.message || \"Not authenticated\"\n                };\n            }\n        } catch (error) {\n            console.error(\"CustomerProvider: Error checking authentication:\", error);\n            return {\n                success: false,\n                message: \"Network error\"\n            };\n        }\n    };\n    // Refresh customer data\n    const refreshCustomer = async ()=>{\n        try {\n            const result = await checkAuthAndGetCustomer();\n            if (result.success) {\n                setCustomer(transformCustomerData(result.customer));\n                console.log(\"Customer data refreshed successfully\");\n            } else {\n                console.log(\"Failed to refresh customer data:\", result.message);\n                setCustomer(null);\n            }\n        } catch (err) {\n            console.error(\"Error refreshing customer data:\", err);\n            setCustomer(null);\n        }\n    };\n    // Check if the customer is logged in on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkCustomerSession = async ()=>{\n            try {\n                setIsLoading(true);\n                // Check authentication and get customer data from API\n                const result = await checkAuthAndGetCustomer();\n                if (result.success) {\n                    console.log(\"Found valid authentication, customer data loaded\");\n                    setCustomer(transformCustomerData(result.customer));\n                } else {\n                    console.log(\"No valid authentication found:\", result.message);\n                    setCustomer(null);\n                }\n            } catch (err) {\n                console.error(\"Error checking customer session:\", err);\n                // On error, clear any potentially corrupted session data\n                (0,_lib_clientAuth__WEBPACK_IMPORTED_MODULE_3__.logout)();\n                setCustomer(null);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        checkCustomerSession();\n    }, []);\n    // Logout function\n    const logout = ()=>{\n        (0,_lib_clientAuth__WEBPACK_IMPORTED_MODULE_3__.logout)();\n        setCustomer(null);\n        // Clear the cart data when the user logs out\n        const cartStore = _lib_wooStore__WEBPACK_IMPORTED_MODULE_6__.useCartStore.getState();\n        // Clear the cart store to ensure all cart data is reset\n        cartStore.clearCart().catch((error)=>{\n            console.error(\"Error clearing cart during logout:\", error);\n        // Even if clearing cart fails, we should still proceed with local cleanup\n        });\n        // Reset cart initialization indicator in sessionStorage\n        if (true) {\n            sessionStorage.removeItem(\"cartInitialized\");\n            // Ensure we clean up any error states\n            sessionStorage.removeItem(\"cartInitializationAttempts\");\n        }\n        // Show info toast notification\n        addToast(\"You have been signed out successfully\", \"info\");\n        router.push(\"/\");\n        router.refresh(); // Refresh to update UI based on auth state\n    };\n    // Parse and handle Shopify authentication errors with more specific messages\n    const parseAuthError = (error)=>{\n        if (!error) return \"An unknown error occurred\";\n        const errorMessage = typeof error === \"string\" ? error : error.message || JSON.stringify(error);\n        // Common Shopify customer auth errors\n        if (errorMessage.includes(\"Unidentified customer\")) {\n            return \"The email or password you entered is incorrect. Please try again.\";\n        }\n        if (errorMessage.includes(\"already associated\")) {\n            return \"An account with this email already exists. Please sign in instead.\";\n        }\n        if (errorMessage.includes(\"password\") && errorMessage.includes(\"too short\")) {\n            return \"Your password must be at least 8 characters. Please try again.\";\n        }\n        if (errorMessage.includes(\"token\") && (errorMessage.includes(\"expired\") || errorMessage.includes(\"invalid\"))) {\n            return \"Your login session has expired. Please sign in again.\";\n        }\n        if (errorMessage.includes(\"network\") || errorMessage.includes(\"failed to fetch\")) {\n            return \"Network connection issue. Please check your internet connection and try again.\";\n        }\n        // Return the original error if no specific handling\n        return errorMessage;\n    };\n    // Enhanced login function with better error handling\n    const login = async (credentials)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await (0,_lib_clientAuth__WEBPACK_IMPORTED_MODULE_3__.login)(credentials.email, credentials.password);\n            if (!result || !result.success || !result.user) {\n                throw new Error(\"Login failed: No user data returned\");\n            }\n            // Convert user to customer format\n            const customer = {\n                id: result.user.id,\n                databaseId: result.user.databaseId,\n                email: result.user.email,\n                firstName: result.user.firstName,\n                lastName: result.user.lastName\n            };\n            setCustomer(transformCustomerData(customer));\n            // Initialize a fresh cart after login to ensure it's associated with the customer\n            const cartStore = _lib_wooStore__WEBPACK_IMPORTED_MODULE_6__.useCartStore.getState();\n            try {\n                await cartStore.clearCart();\n                // Initialize cart with the correct method\n                await cartStore.initializeCart();\n            } catch (cartError) {\n                console.error(\"Error initializing cart after login:\", cartError);\n            // Don't fail the login process if cart initialization fails\n            }\n            // Show success toast notification\n            addToast(\"Welcome back, \".concat((customer === null || customer === void 0 ? void 0 : customer.firstName) || \"there\", \"!\"), \"success\");\n            // Redirect to homepage instead of account page\n            router.push(\"/\");\n        } catch (err) {\n            const errorMessage = parseAuthError(err);\n            setError(errorMessage);\n            // Show error toast notification\n            addToast(errorMessage, \"error\");\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Enhanced register function with better error handling\n    const register = async (registration)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            var _result_customer;\n            const result = await (0,_lib_clientAuth__WEBPACK_IMPORTED_MODULE_3__.register)(registration.email, registration.firstName, registration.lastName, registration.password);\n            if (!result || !result.success || !result.customer) {\n                throw new Error(\"Registration failed: No customer data returned\");\n            }\n            setCustomer(transformCustomerData(result.customer));\n            // Initialize a fresh cart after registration to ensure it's associated with the customer\n            const cartStore = _lib_wooStore__WEBPACK_IMPORTED_MODULE_6__.useCartStore.getState();\n            try {\n                await cartStore.clearCart();\n                // Initialize cart with the correct method\n                await cartStore.initializeCart();\n            } catch (cartError) {\n                console.error(\"Error initializing cart after registration:\", cartError);\n            // Don't fail the registration process if cart initialization fails\n            }\n            // Show success toast notification\n            addToast(\"Welcome to Ankkor, \".concat((_result_customer = result.customer) === null || _result_customer === void 0 ? void 0 : _result_customer.firstName, \"!\"), \"success\");\n            // Redirect to homepage instead of account page\n            router.push(\"/\");\n        } catch (err) {\n            const errorMessage = parseAuthError(err);\n            setError(errorMessage);\n            // Show error toast notification\n            addToast(errorMessage, \"error\");\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Function to update customer profile\n    const updateProfile = async (data)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.updateCustomerProfile)(data);\n            if (!result || !result.customer) {\n                throw new Error(\"Profile update failed: No customer data returned\");\n            }\n            // Update the customer state with the new data\n            setCustomer(transformCustomerData(result.customer));\n            // Show success toast notification\n            addToast(\"Your profile has been updated successfully\", \"success\");\n            return result;\n        } catch (err) {\n            const errorMessage = parseAuthError(err);\n            setError(errorMessage);\n            // Show error toast notification\n            addToast(errorMessage, \"error\");\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Calculate isAuthenticated from customer data\n    const isAuthenticated = Boolean(customer);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomerContext.Provider, {\n        value: {\n            customer,\n            isLoading,\n            isAuthenticated,\n            login,\n            register,\n            logout,\n            updateProfile,\n            error,\n            refreshCustomer\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\CustomerProvider.tsx\",\n        lineNumber: 339,\n        columnNumber: 5\n    }, this);\n}\n_s1(CustomerProvider, \"fHXP75JRrT+/odraAcoNwyr+kK0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_ui_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        _hooks_useAuthCartSync__WEBPACK_IMPORTED_MODULE_7__.useAuthCartSync\n    ];\n});\n_c = CustomerProvider;\nvar _c;\n$RefreshReg$(_c, \"CustomerProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/CustomerProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/LoadingProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/LoadingProvider.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingProvider: function() { return /* binding */ LoadingProvider; },\n/* harmony export */   useLoading: function() { return /* binding */ useLoading; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_PageLoading__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/PageLoading */ \"(app-pages-browser)/./src/components/ui/PageLoading.tsx\");\n/* __next_internal_client_entry_do_not_use__ useLoading,LoadingProvider,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\nconst LoadingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    isLoading: false,\n    setLoading: ()=>{},\n    variant: \"thread\",\n    setVariant: ()=>{}\n});\nconst useLoading = ()=>{\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LoadingContext);\n};\n_s(useLoading, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n// Map paths to specific loader variants for a more tailored experience\nconst pathVariantMap = {\n    \"/collection\": \"fabric\",\n    \"/collection/shirts\": \"fabric\",\n    \"/collection/polos\": \"fabric\",\n    \"/product\": \"thread\",\n    \"/about\": \"button\",\n    \"/customer-service\": \"button\",\n    \"/account\": \"thread\",\n    \"/wishlist\": \"thread\"\n};\n// Separate component that uses useSearchParams\nconst RouteChangeHandler = (param)=>{\n    let { setIsLoading, setVariant } = param;\n    _s1();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Import useSearchParams inside the component that's wrapped with Suspense\n    const { useSearchParams } = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n    const searchParams = useSearchParams();\n    // Set loading state and variant when route changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Start loading\n        setIsLoading(true);\n        // Determine the appropriate variant based on the path\n        const basePathname = \"/\" + pathname.split(\"/\")[1];\n        const newVariant = pathVariantMap[basePathname] || pathVariantMap[pathname] || \"thread\";\n        setVariant(newVariant);\n        // Simulate loading delay (remove in production and rely on actual loading time)\n        const timer = setTimeout(()=>{\n            setIsLoading(false);\n        }, 1200);\n        return ()=>clearTimeout(timer);\n    }, [\n        pathname,\n        searchParams,\n        setIsLoading,\n        setVariant\n    ]);\n    return null;\n};\n_s1(RouteChangeHandler, \"h6p6PpCFmP4Mu5bIMduBzSZThBE=\", true, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = RouteChangeHandler;\n// Loading fallback component\nconst LoadingFallback = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"hidden\",\n        children: \"Loading route...\"\n    }, void 0, false, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\LoadingProvider.tsx\",\n        lineNumber: 74,\n        columnNumber: 31\n    }, undefined);\n_c1 = LoadingFallback;\nconst LoadingProvider = (param)=>{\n    let { children } = param;\n    _s2();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [variant, setVariant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"thread\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingContext.Provider, {\n        value: {\n            isLoading,\n            setLoading: setIsLoading,\n            variant,\n            setVariant\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingFallback, {}, void 0, false, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\LoadingProvider.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 27\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RouteChangeHandler, {\n                    setIsLoading: setIsLoading,\n                    setVariant: setVariant\n                }, void 0, false, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\LoadingProvider.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\LoadingProvider.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageLoading__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isLoading: isLoading,\n                variant: variant\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\LoadingProvider.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\providers\\\\LoadingProvider.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(LoadingProvider, \"oWTpoJhdp4nnyJ9KhdVnjoT8a/4=\");\n_c2 = LoadingProvider;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LoadingProvider);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"RouteChangeHandler\");\n$RefreshReg$(_c1, \"LoadingFallback\");\n$RefreshReg$(_c2, \"LoadingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/LoadingProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/FashionLoader.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/FashionLoader.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst FashionLoader = (param)=>{\n    let { size = \"md\", variant = \"thread\", className = \"\" } = param;\n    // Size mappings\n    const sizeMap = {\n        sm: {\n            container: \"w-16 h-16\",\n            text: \"text-xs\"\n        },\n        md: {\n            container: \"w-24 h-24\",\n            text: \"text-sm\"\n        },\n        lg: {\n            container: \"w-32 h-32\",\n            text: \"text-base\"\n        }\n    };\n    // Thread Loader - Inspired by sewing thread\n    if (variant === \"thread\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative \".concat(sizeMap[size].container),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute inset-0 rounded-full border-2 border-[#e5e2d9]\",\n                            style: {\n                                borderTopColor: \"#2c2c27\",\n                                borderRightColor: \"#2c2c27\"\n                            },\n                            animate: {\n                                rotate: 360\n                            },\n                            transition: {\n                                duration: 1.5,\n                                repeat: Infinity,\n                                ease: \"linear\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute inset-2 rounded-full border-2 border-[#e5e2d9]\",\n                            style: {\n                                borderBottomColor: \"#8a8778\",\n                                borderLeftColor: \"#8a8778\"\n                            },\n                            animate: {\n                                rotate: -360\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"linear\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full bg-[#2c2c27]\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 font-serif text-[#5c5c52] \".concat(sizeMap[size].text),\n                    children: \"Loading Collection\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Fabric Loader - Inspired by fabric swatches\n    if (variant === \"fabric\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative \".concat(sizeMap[size].container, \" flex items-center justify-center\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute w-1/3 h-1/3 bg-[#e5e2d9]\",\n                            animate: {\n                                rotate: 360,\n                                scale: [\n                                    1,\n                                    1.2,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute w-1/3 h-1/3 bg-[#8a8778]\",\n                            animate: {\n                                rotate: -360,\n                                scale: [\n                                    1,\n                                    0.8,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: 0.3\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute w-1/3 h-1/3 bg-[#2c2c27]\",\n                            animate: {\n                                rotate: 360,\n                                scale: [\n                                    1,\n                                    0.8,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: 0.6\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 font-serif text-[#5c5c52] \".concat(sizeMap[size].text),\n                    children: \"Preparing Your Style\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Button Loader - Inspired by clothing buttons\n    if (variant === \"button\") {\n        const buttons = [\n            0,\n            1,\n            2,\n            3\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative \".concat(sizeMap[size].container, \" flex items-center justify-center\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex\",\n                        children: buttons.map((index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                className: \"w-3 h-3 mx-1 rounded-full bg-[#2c2c27] border border-[#8a8778]\",\n                                animate: {\n                                    y: [\n                                        0,\n                                        -10,\n                                        0\n                                    ],\n                                    opacity: [\n                                        0.5,\n                                        1,\n                                        0.5\n                                    ]\n                                },\n                                transition: {\n                                    duration: 1,\n                                    repeat: Infinity,\n                                    ease: \"easeInOut\",\n                                    delay: index * 0.2\n                                }\n                            }, index, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 font-serif text-[#5c5c52] \".concat(sizeMap[size].text),\n                    children: \"Tailoring Experience\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Default fallback\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative \".concat(sizeMap[size].container),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"absolute inset-0 rounded-full border-2 border-[#e5e2d9]\",\n                    style: {\n                        borderTopColor: \"#2c2c27\"\n                    },\n                    animate: {\n                        rotate: 360\n                    },\n                    transition: {\n                        duration: 1,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-4 font-serif text-[#5c5c52] \".concat(sizeMap[size].text),\n                children: \"Loading\"\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\FashionLoader.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, undefined);\n};\n_c = FashionLoader;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FashionLoader);\nvar _c;\n$RefreshReg$(_c, \"FashionLoader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/FashionLoader.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/PageLoading.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/PageLoading.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _FashionLoader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FashionLoader */ \"(app-pages-browser)/./src/components/ui/FashionLoader.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst PageLoading = (param)=>{\n    let { isLoading, variant = \"thread\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n        children: isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            transition: {\n                duration: 0.3\n            },\n            className: \"fixed inset-0 z-[200] flex items-center justify-center bg-[#f8f8f5]/90 backdrop-blur-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FashionLoader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                variant: variant,\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\PageLoading.tsx\",\n                lineNumber: 23,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\PageLoading.tsx\",\n            lineNumber: 16,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\ui\\\\PageLoading.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PageLoading;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PageLoading);\nvar _c;\n$RefreshReg$(_c, \"PageLoading\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/PageLoading.tsx\n"));

/***/ })

});
// runtime can't be in strict mode because a global variable is assign and maybe created.
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page-src_lib_woocommerce_ts-ea0e4c9f"],{

/***/ "(app-pages-browser)/./src/lib/woocommerce.ts":
/*!********************************!*\
  !*** ./src/lib/woocommerce.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADD_TO_CART: function() { return /* binding */ ADD_TO_CART; },\n/* harmony export */   GET_CART: function() { return /* binding */ GET_CART; },\n/* harmony export */   MUTATION_LOGIN: function() { return /* binding */ MUTATION_LOGIN; },\n/* harmony export */   MUTATION_REMOVE_FROM_CART: function() { return /* binding */ MUTATION_REMOVE_FROM_CART; },\n/* harmony export */   QUERY_ALL_CATEGORIES: function() { return /* binding */ QUERY_ALL_CATEGORIES; },\n/* harmony export */   QUERY_ALL_PRODUCTS: function() { return /* binding */ QUERY_ALL_PRODUCTS; },\n/* harmony export */   QUERY_CATEGORY_PRODUCTS: function() { return /* binding */ QUERY_CATEGORY_PRODUCTS; },\n/* harmony export */   QUERY_GET_CART: function() { return /* binding */ QUERY_GET_CART; },\n/* harmony export */   QUERY_PAYMENT_GATEWAYS: function() { return /* binding */ QUERY_PAYMENT_GATEWAYS; },\n/* harmony export */   QUERY_SHIPPING_METHODS: function() { return /* binding */ QUERY_SHIPPING_METHODS; },\n/* harmony export */   addToCart: function() { return /* binding */ addToCart; },\n/* harmony export */   clearAuthToken: function() { return /* binding */ clearAuthToken; },\n/* harmony export */   createAddress: function() { return /* binding */ createAddress; },\n/* harmony export */   createCart: function() { return /* binding */ createCart; },\n/* harmony export */   createCustomer: function() { return /* binding */ createCustomer; },\n/* harmony export */   customerLogin: function() { return /* binding */ customerLogin; },\n/* harmony export */   deleteAddress: function() { return /* binding */ deleteAddress; },\n/* harmony export */   fetchFromWooCommerce: function() { return /* binding */ fetchFromWooCommerce; },\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   getAllCategories: function() { return /* binding */ getAllCategories; },\n/* harmony export */   getAllProducts: function() { return /* binding */ getAllProducts; },\n/* harmony export */   getCart: function() { return /* binding */ getCart; },\n/* harmony export */   getCategories: function() { return /* binding */ getCategories; },\n/* harmony export */   getCategoryProducts: function() { return /* binding */ getCategoryProducts; },\n/* harmony export */   getCategoryProductsWithTags: function() { return /* binding */ getCategoryProductsWithTags; },\n/* harmony export */   getCustomer: function() { return /* binding */ getCustomer; },\n/* harmony export */   getMetafield: function() { return /* binding */ getMetafield; },\n/* harmony export */   getProduct: function() { return /* binding */ getProduct; },\n/* harmony export */   getProductById: function() { return /* binding */ getProductById; },\n/* harmony export */   getProductBySlug: function() { return /* binding */ getProductBySlug; },\n/* harmony export */   getProductBySlugWithTags: function() { return /* binding */ getProductBySlugWithTags; },\n/* harmony export */   getProductVariations: function() { return /* binding */ getProductVariations; },\n/* harmony export */   getProducts: function() { return /* binding */ getProducts; },\n/* harmony export */   getSessionToken: function() { return /* binding */ getSessionToken; },\n/* harmony export */   getWooCommerceCheckoutUrl: function() { return /* binding */ getWooCommerceCheckoutUrl; },\n/* harmony export */   normalizeCart: function() { return /* binding */ normalizeCart; },\n/* harmony export */   normalizeCategory: function() { return /* binding */ normalizeCategory; },\n/* harmony export */   normalizeProduct: function() { return /* binding */ normalizeProduct; },\n/* harmony export */   normalizeProductImages: function() { return /* binding */ normalizeProductImages; },\n/* harmony export */   removeFromCart: function() { return /* binding */ removeFromCart; },\n/* harmony export */   searchProducts: function() { return /* binding */ searchProducts; },\n/* harmony export */   setAuthToken: function() { return /* binding */ setAuthToken; },\n/* harmony export */   setDefaultAddress: function() { return /* binding */ setDefaultAddress; },\n/* harmony export */   setSessionToken: function() { return /* binding */ setSessionToken; },\n/* harmony export */   updateAddress: function() { return /* binding */ updateAddress; },\n/* harmony export */   updateCart: function() { return /* binding */ updateCart; },\n/* harmony export */   updateCustomer: function() { return /* binding */ updateCustomer; },\n/* harmony export */   wooConfig: function() { return /* binding */ wooConfig; },\n/* harmony export */   wooGraphQLFetch: function() { return /* binding */ wooGraphQLFetch; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var graphql_request__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! graphql-request */ \"(app-pages-browser)/./node_modules/graphql-request/build/entrypoints/main.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// WooCommerce GraphQL API integration - Fixed according to official documentation\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  fragment ProductFields on Product {\\n    id\\n    databaseId\\n    name\\n    slug\\n    description\\n    shortDescription\\n    type\\n    image {\\n      sourceUrl\\n      altText\\n    }\\n    galleryImages {\\n      nodes {\\n        sourceUrl\\n        altText\\n      }\\n    }\\n    ... on SimpleProduct {\\n      price\\n      regularPrice\\n      salePrice\\n      onSale\\n      stockStatus\\n      stockQuantity\\n    }\\n    ... on VariableProduct {\\n      price\\n      regularPrice\\n      salePrice\\n      onSale\\n      stockStatus\\n      stockQuantity\\n      attributes {\\n        nodes {\\n          name\\n          options\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  fragment VariableProductWithVariations on VariableProduct {\\n    attributes {\\n      nodes {\\n        name\\n        options\\n      }\\n    }\\n    variations {\\n      nodes {\\n        id\\n        databaseId\\n        name\\n        price\\n        regularPrice\\n        salePrice\\n        stockStatus\\n        stockQuantity\\n        attributes {\\n          nodes {\\n            name\\n            value\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetProducts(\\n    $first: Int\\n    $after: String\\n    $where: RootQueryToProductConnectionWhereArgs\\n  ) {\\n    products(first: $first, after: $after, where: $where) {\\n      pageInfo {\\n        hasNextPage\\n        endCursor\\n      }\\n      nodes {\\n        ...ProductFields\\n        ... on VariableProduct {\\n          ...VariableProductWithVariations\\n        }\\n      }\\n    }\\n  }\\n  \",\n        \"\\n  \",\n        \"\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetProductBySlug($slug: ID!) {\\n    product(id: $slug, idType: SLUG) {\\n      ...ProductFields\\n      ... on VariableProduct {\\n        ...VariableProductWithVariations\\n      }\\n    }\\n  }\\n  \",\n        \"\\n  \",\n        \"\\n\"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetProductBySlugWithTags($slug: ID!) {\\n    product(id: $slug, idType: SLUG) {\\n      ...ProductFields\\n      ... on VariableProduct {\\n        ...VariableProductWithVariations\\n      }\\n      productTags {\\n        nodes {\\n          id\\n          name\\n          slug\\n        }\\n      }\\n      productCategories {\\n        nodes {\\n          id\\n          name\\n          slug\\n        }\\n      }\\n    }\\n  }\\n  \",\n        \"\\n  \",\n        \"\\n\"\n    ]);\n    _templateObject4 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject5() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetCategories(\\n    $first: Int\\n    $after: String\\n    $where: RootQueryToProductCategoryConnectionWhereArgs\\n  ) {\\n    productCategories(first: $first, after: $after, where: $where) {\\n      pageInfo {\\n        hasNextPage\\n        endCursor\\n      }\\n      nodes {\\n        id\\n        databaseId\\n        name\\n        slug\\n        description\\n        count\\n        image {\\n          sourceUrl\\n          altText\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject5 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject6() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      query GetProductVariations($id: ID!) {\\n        product(id: $id, idType: DATABASE_ID) {\\n          ... on VariableProduct {\\n            variations {\\n              nodes {\\n                id\\n                databaseId\\n                name\\n                price\\n                regularPrice\\n                salePrice\\n                stockStatus\\n                attributes {\\n                  nodes {\\n                    name\\n                    value\\n                  }\\n                }\\n              }\\n            }\\n          }\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject6 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject7() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetAllProducts($first: Int = 20) {\\n    products(first: $first) {\\n      nodes {\\n        id\\n        databaseId\\n        name\\n        slug\\n        description\\n        shortDescription\\n        productCategories {\\n          nodes {\\n            id\\n            name\\n            slug\\n          }\\n        }\\n        ... on SimpleProduct {\\n          price\\n          regularPrice\\n          salePrice\\n          onSale\\n          stockStatus\\n          stockQuantity\\n        }\\n        ... on VariableProduct {\\n          price\\n          regularPrice\\n          salePrice\\n          onSale\\n          stockStatus\\n          variations {\\n            nodes {\\n              stockStatus\\n              stockQuantity\\n            }\\n          }\\n        }\\n        image {\\n          id\\n          sourceUrl\\n          altText\\n        }\\n        galleryImages {\\n          nodes {\\n            id\\n            sourceUrl\\n            altText\\n          }\\n        }\\n        ... on VariableProduct {\\n          attributes {\\n            nodes {\\n              name\\n              options\\n            }\\n          }\\n        }\\n        ... on SimpleProduct {\\n          attributes {\\n            nodes {\\n              name\\n              options\\n            }\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject7 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject8() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetProductsByCategory($slug: ID!, $first: Int = 20) {\\n    productCategory(id: $slug, idType: SLUG) {\\n      id\\n      name\\n      slug\\n      description\\n      products(first: $first) {\\n        nodes {\\n          id\\n          databaseId\\n          name\\n          slug\\n          ... on SimpleProduct {\\n            price\\n            regularPrice\\n            salePrice\\n            onSale\\n            stockStatus\\n          }\\n          ... on VariableProduct {\\n            price\\n            regularPrice\\n            salePrice\\n            onSale\\n            stockStatus\\n          }\\n          image {\\n            id\\n            sourceUrl\\n            altText\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject8 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject9() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetAllCategories($first: Int = 20) {\\n    productCategories(first: $first) {\\n      nodes {\\n        id\\n        databaseId\\n        name\\n        slug\\n        description\\n        count\\n        image {\\n          sourceUrl\\n          altText\\n        }\\n        children {\\n          nodes {\\n            id\\n            name\\n            slug\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject9 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject10() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetCart {\\n    cart {\\n      contents {\\n        nodes {\\n          key\\n          product {\\n            node {\\n              id\\n              databaseId\\n              name\\n              slug\\n              type\\n              image {\\n                sourceUrl\\n                altText\\n              }\\n            }\\n          }\\n          variation {\\n            node {\\n              id\\n              databaseId\\n              name\\n              attributes {\\n                nodes {\\n                  name\\n                  value\\n                }\\n              }\\n            }\\n          }\\n          quantity\\n          total\\n        }\\n      }\\n      subtotal\\n      total\\n      totalTax\\n      isEmpty\\n    }\\n  }\\n\"\n    ]);\n    _templateObject10 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject11() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        '\\n  mutation LoginUser($username: String!, $password: String!) {\\n    login(input: {\\n      clientMutationId: \"login\"\\n      username: $username\\n      password: $password\\n    }) {\\n      authToken\\n      refreshToken\\n      user {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n        nicename\\n        nickname\\n        username\\n      }\\n    }\\n  }\\n'\n    ]);\n    _templateObject11 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject12() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetCart {\\n    cart {\\n      contents {\\n        nodes {\\n          key\\n          product {\\n            node {\\n              id\\n              databaseId\\n              name\\n              slug\\n              type\\n              image {\\n                sourceUrl\\n                altText\\n              }\\n            }\\n          }\\n          variation {\\n            node {\\n              id\\n              databaseId\\n              name\\n              attributes {\\n                nodes {\\n                  name\\n                  value\\n                }\\n              }\\n            }\\n          }\\n          quantity\\n          total\\n        }\\n      }\\n      subtotal\\n      total\\n      totalTax\\n      isEmpty\\n      contentsCount\\n    }\\n  }\\n\"\n    ]);\n    _templateObject12 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject13() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        '\\n  mutation AddToCart($productId: Int!, $variationId: Int, $quantity: Int, $extraData: String) {\\n    addToCart(\\n      input: {\\n        clientMutationId: \"addToCart\"\\n        productId: $productId\\n        variationId: $variationId\\n        quantity: $quantity\\n        extraData: $extraData\\n      }\\n    ) {\\n      cart {\\n        contents {\\n          nodes {\\n            key\\n            product {\\n              node {\\n                id\\n                databaseId\\n                name\\n                slug\\n                type\\n                image {\\n                  sourceUrl\\n                  altText\\n                }\\n              }\\n            }\\n            variation {\\n              node {\\n                id\\n                databaseId\\n                name\\n                attributes {\\n                  nodes {\\n                    name\\n                    value\\n                  }\\n                }\\n              }\\n            }\\n            quantity\\n            total\\n          }\\n        }\\n        subtotal\\n        total\\n        totalTax\\n        isEmpty\\n        contentsCount\\n      }\\n    }\\n  }\\n'\n    ]);\n    _templateObject13 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject14() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation RemoveItemsFromCart($keys: [ID]!, $all: Boolean) {\\n    removeItemsFromCart(input: { keys: $keys, all: $all }) {\\n      cart {\\n        contents {\\n          nodes {\\n            key\\n            product {\\n              node {\\n                id\\n                databaseId\\n                name\\n                slug\\n                type\\n                image {\\n                  sourceUrl\\n                  altText\\n                }\\n              }\\n            }\\n            variation {\\n              node {\\n                id\\n                databaseId\\n                name\\n                attributes {\\n                  nodes {\\n                    name\\n                    value\\n                  }\\n                }\\n              }\\n            }\\n            quantity\\n            total\\n          }\\n        }\\n        subtotal\\n        total\\n        totalTax\\n        isEmpty\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject14 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject15() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetShippingMethods {\\n    shippingMethods {\\n      nodes {\\n        id\\n        title\\n        description\\n        cost\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject15 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject16() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetPaymentGateways {\\n    paymentGateways {\\n      nodes {\\n        id\\n        title\\n        description\\n        enabled\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject16 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject17() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        '\\n      mutation LoginUser($username: String!, $password: String!) {\\n        login(input: {\\n          clientMutationId: \"login\"\\n          username: $username\\n          password: $password\\n        }) {\\n          authToken\\n          refreshToken\\n          user {\\n            id\\n            databaseId\\n            email\\n            firstName\\n            lastName\\n            nicename\\n            nickname\\n            username\\n          }\\n        }\\n      }\\n    '\n    ]);\n    _templateObject17 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject18() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      mutation RegisterUser($input: RegisterCustomerInput!) {\\n        registerCustomer(input: $input) {\\n          clientMutationId\\n          authToken\\n          refreshToken\\n          customer {\\n            id\\n            databaseId\\n            email\\n            firstName\\n            lastName\\n          }\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject18 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject19() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      query GetCustomer {\\n        customer {\\n          id\\n          databaseId\\n          email\\n          firstName\\n          lastName\\n          displayName\\n          username\\n          role\\n          date\\n          modified\\n          isPayingCustomer\\n          orderCount\\n          totalSpent\\n          billing {\\n            firstName\\n            lastName\\n            company\\n            address1\\n            address2\\n            city\\n            state\\n            postcode\\n            country\\n            email\\n            phone\\n          }\\n          shipping {\\n            firstName\\n            lastName\\n            company\\n            address1\\n            address2\\n            city\\n            state\\n            postcode\\n            country\\n          }\\n          orders(first: 50) {\\n            nodes {\\n              id\\n              databaseId\\n              date\\n              status\\n              total\\n              subtotal\\n              totalTax\\n              shippingTotal\\n              discountTotal\\n              paymentMethodTitle\\n              customerNote\\n              billing {\\n                firstName\\n                lastName\\n                company\\n                address1\\n                address2\\n                city\\n                state\\n                postcode\\n                country\\n                email\\n                phone\\n              }\\n              shipping {\\n                firstName\\n                lastName\\n                company\\n                address1\\n                address2\\n                city\\n                state\\n                postcode\\n                country\\n              }\\n              lineItems {\\n                nodes {\\n                  product {\\n                    node {\\n                      id\\n                      name\\n                      slug\\n                      image {\\n                        sourceUrl\\n                        altText\\n                      }\\n                    }\\n                  }\\n                  variation {\\n                    node {\\n                      id\\n                      name\\n                      attributes {\\n                        nodes {\\n                          name\\n                          value\\n                        }\\n                      }\\n                    }\\n                  }\\n                  quantity\\n                  total\\n                  subtotal\\n                  totalTax\\n                }\\n              }\\n              shippingLines {\\n                nodes {\\n                  methodTitle\\n                  total\\n                }\\n              }\\n              feeLines {\\n                nodes {\\n                  name\\n                  total\\n                }\\n              }\\n              couponLines {\\n                nodes {\\n                  code\\n                  discount\\n                }\\n              }\\n            }\\n          }\\n          downloadableItems {\\n            nodes {\\n              name\\n              downloadId\\n              downloadsRemaining\\n              accessExpires\\n              product {\\n                node {\\n                  id\\n                  name\\n                }\\n              }\\n            }\\n          }\\n          metaData {\\n            key\\n            value\\n          }\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject19 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject20() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      query GetProductById($id: ID!) {\\n        product(id: $id, idType: DATABASE_ID) {\\n          id\\n          databaseId\\n          name\\n          slug\\n          description\\n          shortDescription\\n          productCategories {\\n            nodes {\\n              id\\n              name\\n              slug\\n            }\\n          }\\n          ... on SimpleProduct {\\n            price\\n            regularPrice\\n            salePrice\\n            onSale\\n            stockStatus\\n            stockQuantity\\n          }\\n          ... on VariableProduct {\\n            price\\n            regularPrice\\n            salePrice\\n            onSale\\n            stockStatus\\n            variations {\\n              nodes {\\n                stockStatus\\n                stockQuantity\\n              }\\n            }\\n          }\\n          image {\\n            id\\n            sourceUrl\\n            altText\\n          }\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject20 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject21() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n    query SearchProducts($query: String!, $first: Int) {\\n      products(first: $first, where: { search: $query }) {\\n        nodes {\\n          id\\n          databaseId\\n          name\\n          slug\\n          price\\n          image {\\n            sourceUrl\\n            altText\\n          }\\n          shortDescription\\n        }\\n        pageInfo {\\n          hasNextPage\\n          endCursor\\n        }\\n      }\\n    }\\n  \"\n    ]);\n    _templateObject21 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject22() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n    query GetProduct($id: ID!) {\\n      product(id: $id, idType: DATABASE_ID) {\\n        id\\n        databaseId\\n        name\\n        slug\\n        description\\n        shortDescription\\n        price\\n        regularPrice\\n        salePrice\\n        onSale\\n        stockStatus\\n        stockQuantity\\n        image {\\n          sourceUrl\\n          altText\\n        }\\n        galleryImages {\\n          nodes {\\n            sourceUrl\\n            altText\\n          }\\n        }\\n        ... on SimpleProduct {\\n          attributes {\\n            nodes {\\n              name\\n              options\\n            }\\n          }\\n          price\\n          regularPrice\\n          salePrice\\n        }\\n        ... on VariableProduct {\\n          price\\n          regularPrice\\n          salePrice\\n          attributes {\\n            nodes {\\n              name\\n              options\\n            }\\n          }\\n          variations {\\n            nodes {\\n              id\\n              databaseId\\n              name\\n              price\\n              regularPrice\\n              salePrice\\n              stockStatus\\n              attributes {\\n                nodes {\\n                  name\\n                  value\\n                }\\n              }\\n            }\\n          }\\n        }\\n      }\\n    }\\n  \"\n    ]);\n    _templateObject22 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject23() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation CreateCustomer($input: RegisterCustomerInput!) {\\n    registerCustomer(input: $input) {\\n      customer {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n        displayName\\n      }\\n      authToken\\n      refreshToken\\n    }\\n  }\\n\"\n    ]);\n    _templateObject23 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject24() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation UpdateCustomer($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      clientMutationId\\n      customer {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n        displayName\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n      customerUserErrors {\\n        field\\n        message\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject24 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject25() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetCustomer {\\n    customer {\\n      id\\n      databaseId\\n      email\\n      firstName\\n      lastName\\n      displayName\\n      username\\n      role\\n      date\\n      modified\\n      isPayingCustomer\\n      orderCount\\n      totalSpent\\n      billing {\\n        firstName\\n        lastName\\n        company\\n        address1\\n        address2\\n        city\\n        state\\n        postcode\\n        country\\n        email\\n        phone\\n      }\\n      shipping {\\n        firstName\\n        lastName\\n        company\\n        address1\\n        address2\\n        city\\n        state\\n        postcode\\n        country\\n      }\\n      orders(first: 50) {\\n        nodes {\\n          id\\n          databaseId\\n          date\\n          status\\n          total\\n          subtotal\\n          totalTax\\n          shippingTotal\\n          discountTotal\\n          paymentMethodTitle\\n          customerNote\\n          billing {\\n            firstName\\n            lastName\\n            company\\n            address1\\n            address2\\n            city\\n            state\\n            postcode\\n            country\\n            email\\n            phone\\n          }\\n          shipping {\\n            firstName\\n            lastName\\n            company\\n            address1\\n            address2\\n            city\\n            state\\n            postcode\\n            country\\n          }\\n          lineItems {\\n            nodes {\\n              product {\\n                node {\\n                  id\\n                  name\\n                  slug\\n                  image {\\n                    sourceUrl\\n                    altText\\n                  }\\n                }\\n              }\\n              variation {\\n                node {\\n                  id\\n                  name\\n                  attributes {\\n                    nodes {\\n                      name\\n                      value\\n                    }\\n                  }\\n                }\\n              }\\n              quantity\\n              total\\n              subtotal\\n              totalTax\\n            }\\n          }\\n          shippingLines {\\n            nodes {\\n              methodTitle\\n              total\\n            }\\n          }\\n          feeLines {\\n            nodes {\\n              name\\n              total\\n            }\\n          }\\n          couponLines {\\n            nodes {\\n              code\\n              discount\\n            }\\n          }\\n        }\\n      }\\n      downloadableItems {\\n        nodes {\\n          name\\n          downloadId\\n          downloadsRemaining\\n          accessExpires\\n          product {\\n            node {\\n              id\\n              name\\n            }\\n          }\\n        }\\n      }\\n      metaData {\\n        key\\n        value\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject25 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject26() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation CreateAddress($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      customer {\\n        id\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject26 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject27() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation UpdateAddress($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      customer {\\n        id\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject27 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject28() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation DeleteAddress($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      customer {\\n        id\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject28 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject29() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation SetDefaultAddress($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      customer {\\n        id\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject29 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject30() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation UpdateCart($input: UpdateItemQuantitiesInput!) {\\n    updateItemQuantities(input: $input) {\\n      cart {\\n        contents {\\n          nodes {\\n            key\\n            product {\\n              node {\\n                id\\n                name\\n                price\\n              }\\n            }\\n            quantity\\n            total\\n          }\\n        }\\n        subtotal\\n        total\\n        totalTax\\n        isEmpty\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject30 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n// WooCommerce store configuration\nconst wooConfig = {\n    storeUrl: \"https://deepskyblue-penguin-370791.hostingersite.com\" || 0,\n    graphqlUrl: process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\",\n    apiVersion: \"v1\"\n};\n// Session management for WooCommerce\nlet sessionToken = null;\nconst getSessionToken = ()=>{\n    if (true) {\n        return sessionStorage.getItem(\"woo-session-token\") || sessionToken;\n    }\n    return sessionToken;\n};\nconst setSessionToken = (token)=>{\n    sessionToken = token;\n    if (true) {\n        if (token) {\n            sessionStorage.setItem(\"woo-session-token\", token);\n        } else {\n            sessionStorage.removeItem(\"woo-session-token\");\n        }\n    }\n};\n// Check if code is running on client or server\nconst isClient = \"object\" !== \"undefined\";\n// Initialize GraphQL client with proper headers for CORS\nconst endpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\";\nconst graphQLClient = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n    headers: {\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\"\n    }\n});\n// Set auth token for authenticated requests\nconst setAuthToken = (token)=>{\n    graphQLClient.setHeader(\"Authorization\", \"Bearer \".concat(token));\n};\n// Clear auth token for unauthenticated requests\nconst clearAuthToken = ()=>{\n    graphQLClient.setHeaders({\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\"\n    });\n};\n// GraphQL fragments\nconst PRODUCT_FRAGMENT = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject());\n// Define a separate fragment for variable products with variations\nconst VARIABLE_PRODUCT_FRAGMENT = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject1());\n// Queries\nconst GET_PRODUCTS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject2(), PRODUCT_FRAGMENT, VARIABLE_PRODUCT_FRAGMENT);\nconst GET_PRODUCT_BY_SLUG = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject3(), PRODUCT_FRAGMENT, VARIABLE_PRODUCT_FRAGMENT);\nconst GET_PRODUCT_BY_SLUG_WITH_TAGS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject4(), PRODUCT_FRAGMENT, VARIABLE_PRODUCT_FRAGMENT);\nconst GET_CATEGORIES = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject5());\n// Fetch functions\nasync function getProducts() {\n    let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    try {\n        const data = await fetchFromWooCommerce(GET_PRODUCTS, {\n            first: variables.first || 12,\n            after: variables.after || null,\n            where: variables.where || {}\n        }, [\n            \"products\"\n        ], 60);\n        return data.products;\n    } catch (error) {\n        console.error(\"Error fetching products:\", error);\n        return {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    }\n}\n/**\n * Get variations for a variable product\n */ async function getProductVariations(productId) {\n    try {\n        var _response_product_variations, _response_product;\n        const query = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject6());\n        const response = await fetchFromWooCommerce(query, {\n            id: productId\n        }, [\n            \"product-\".concat(productId),\n            \"products\"\n        ], 60);\n        return ((_response_product = response.product) === null || _response_product === void 0 ? void 0 : (_response_product_variations = _response_product.variations) === null || _response_product_variations === void 0 ? void 0 : _response_product_variations.nodes) || [];\n    } catch (error) {\n        console.error(\"Error fetching product variations:\", error);\n        return [];\n    }\n}\n/**\n * Get a product by its slug\n */ async function getProductBySlug(slug) {\n    try {\n        const data = await fetchFromWooCommerce(GET_PRODUCT_BY_SLUG, {\n            slug\n        }, [\n            \"product-\".concat(slug),\n            \"products\"\n        ], 60);\n        const product = data.product;\n        // If it's a variable product, fetch variations separately\n        if (product && product.type === \"VARIABLE\") {\n            const variations = await getProductVariations(product.databaseId);\n            return {\n                ...product,\n                variations: {\n                    nodes: variations\n                }\n            };\n        }\n        return product;\n    } catch (error) {\n        console.error(\"Error fetching product by slug:\", error);\n        return null;\n    }\n}\nasync function getProductBySlugWithTags(slug) {\n    try {\n        const data = await fetchFromWooCommerce(GET_PRODUCT_BY_SLUG_WITH_TAGS, {\n            slug\n        }, [\n            \"product-\".concat(slug),\n            \"products\"\n        ], 60);\n        return data.product;\n    } catch (error) {\n        console.error(\"Error fetching product with slug \".concat(slug, \":\"), error);\n        return null;\n    }\n}\n// Categories functionality is now handled by the more comprehensive getAllCategories function\n// Helper function to format price\nfunction formatPrice(price) {\n    const numericPrice = typeof price === \"string\" ? parseFloat(price) : price;\n    return numericPrice.toFixed(2);\n}\n/**\n * Fetch data from WooCommerce GraphQL API with caching and revalidation\n */ async function fetchFromWooCommerce(query) {\n    let variables = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, tags = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [], revalidate = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 60;\n    try {\n        // Use different approaches for client and server\n        if (isClient) {\n            // When on client, use our proxy API route to avoid CORS issues\n            const proxyEndpoint = \"/api/graphql\";\n            // Build the fetch options with session token\n            const headers = {\n                \"Content-Type\": \"application/json\"\n            };\n            // Add session token if available\n            const sessionToken = getSessionToken();\n            if (sessionToken) {\n                headers[\"woocommerce-session\"] = \"Session \".concat(sessionToken);\n            }\n            const fetchOptions = {\n                method: \"POST\",\n                headers,\n                body: JSON.stringify({\n                    query,\n                    variables\n                })\n            };\n            // Make the fetch request through our proxy\n            const response = await fetch(proxyEndpoint, fetchOptions);\n            if (!response.ok) {\n                throw new Error(\"GraphQL API responded with status \".concat(response.status));\n            }\n            // Extract session token from response headers if available\n            const responseSessionHeader = response.headers.get(\"woocommerce-session\");\n            if (responseSessionHeader) {\n                const token = responseSessionHeader.replace(\"Session \", \"\");\n                setSessionToken(token);\n            }\n            const { data, errors } = await response.json();\n            if (errors) {\n                console.error(\"GraphQL Errors:\", errors);\n                throw new Error(errors[0].message);\n            }\n            return data;\n        } else {\n            // Server-side code can directly access the WooCommerce GraphQL endpoint\n            // Build the fetch options with cache control\n            const fetchOptions = {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    query,\n                    variables\n                }),\n                next: {}\n            };\n            // Add cache tags if provided\n            if (tags && tags.length > 0) {\n                fetchOptions.next.tags = tags;\n            }\n            // Add revalidation if provided\n            if (revalidate !== undefined) {\n                fetchOptions.next.revalidate = revalidate;\n            }\n            // Make the fetch request\n            const response = await fetch(wooConfig.graphqlUrl, fetchOptions);\n            if (!response.ok) {\n                throw new Error(\"WooCommerce GraphQL API responded with status \".concat(response.status));\n            }\n            const { data, errors } = await response.json();\n            if (errors) {\n                console.error(\"GraphQL Errors:\", errors);\n                throw new Error(errors[0].message);\n            }\n            return data;\n        }\n    } catch (error) {\n        console.error(\"Error fetching from WooCommerce:\", error);\n        throw error;\n    }\n}\n/**\n * Base implementation of WooCommerce fetch that can be used by other modules\n * This provides a standardized way to make WooGraphQL API requests with retry logic\n * \n * @param query GraphQL query to execute \n * @param variables Variables for the GraphQL query\n * @param retries Number of retries in case of failure\n * @param delay Delay between retries in milliseconds\n * @returns The fetched data\n */ async function wooGraphQLFetch(param) {\n    let { query, variables } = param, retries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n    let attemptCount = 0;\n    let lastError = null;\n    while(attemptCount < retries){\n        try {\n            // Use fetchFromWooCommerce for the actual request, but ignore caching controls\n            // as this is the low-level function that might be used in different contexts\n            const data = await fetchFromWooCommerce(query, variables, [], 0);\n            return data;\n        } catch (error) {\n            lastError = error;\n            attemptCount++;\n            if (attemptCount < retries) {\n                console.log(\"Retrying request (\".concat(attemptCount, \"/\").concat(retries, \") after \").concat(delay, \"ms\"));\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n                // Exponential backoff\n                delay *= 2;\n            }\n        }\n    }\n    console.error(\"Failed after \".concat(retries, \" attempts:\"), lastError);\n    throw lastError;\n}\n/**\n * Get products by category with cache tags for efficient revalidation\n * \n * @param slug The category slug\n * @param first Number of products to fetch\n * @param revalidate Revalidation period in seconds (optional)\n * @returns The category with products\n */ async function getCategoryProductsWithTags(slug) {\n    let first = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20, revalidate = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 60;\n    try {\n        // Define cache tags for this category\n        const tags = [\n            \"category-\".concat(slug),\n            \"categories\",\n            \"products\"\n        ];\n        // Fetch the category with tags\n        const data = await fetchFromWooCommerce(QUERY_CATEGORY_PRODUCTS, {\n            slug,\n            first\n        }, tags, revalidate);\n        return (data === null || data === void 0 ? void 0 : data.productCategory) || null;\n    } catch (error) {\n        console.error(\"Error fetching category with slug \".concat(slug, \":\"), error);\n        throw error;\n    }\n}\n// GraphQL query to fetch all products\nconst QUERY_ALL_PRODUCTS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject7());\n// GraphQL query to fetch products by category\nconst QUERY_CATEGORY_PRODUCTS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject8());\n// GraphQL query to fetch all categories\nconst QUERY_ALL_CATEGORIES = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject9());\n// GraphQL query to get cart contents - Updated for current WooGraphQL schema\nconst QUERY_GET_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject10());\n// Mutation for customer login\nconst MUTATION_LOGIN = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject11());\n// Get cart query - WooCommerce automatically creates a cart when needed\nconst GET_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject12());\n// Add to cart mutation - Updated for current WooGraphQL schema\nconst ADD_TO_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject13());\n// Remove from cart mutation - Updated for current WooGraphQL schema\nconst MUTATION_REMOVE_FROM_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject14());\n// Shipping and payment related queries\nconst QUERY_SHIPPING_METHODS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject15());\nconst QUERY_PAYMENT_GATEWAYS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject16());\n// Implement core API methods\n/**\n * Get all products with pagination\n */ async function getAllProducts() {\n    let first = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20;\n    try {\n        var _data_products;\n        const data = await wooGraphQLFetch({\n            query: QUERY_ALL_PRODUCTS,\n            variables: {\n                first\n            }\n        });\n        return (data === null || data === void 0 ? void 0 : (_data_products = data.products) === null || _data_products === void 0 ? void 0 : _data_products.nodes) || [];\n    } catch (error) {\n        console.error(\"Error fetching all products:\", error);\n        return [];\n    }\n}\n/**\n * Get all categories with pagination\n */ async function getAllCategories() {\n    let first = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20;\n    try {\n        var _data_productCategories;\n        const data = await wooGraphQLFetch({\n            query: QUERY_ALL_CATEGORIES,\n            variables: {\n                first\n            }\n        });\n        return (data === null || data === void 0 ? void 0 : (_data_productCategories = data.productCategories) === null || _data_productCategories === void 0 ? void 0 : _data_productCategories.nodes) || [];\n    } catch (error) {\n        console.error(\"Error fetching all categories:\", error);\n        return [];\n    }\n}\n/**\n * Get product categories with pagination and filtering\n * @param variables Object containing:\n *   - first: Number of categories to return (default: 20)\n *   - after: Cursor for pagination\n *   - where: Filter criteria (parent, search, etc.)\n * @returns Object containing categories and pagination info\n */ async function getCategories() {\n    let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    try {\n        const result = await wooGraphQLFetch({\n            query: QUERY_ALL_CATEGORIES,\n            variables: {\n                first: variables.first || 20,\n                after: variables.after || null,\n                where: variables.where || {}\n            }\n        });\n        return {\n            nodes: result.productCategories.nodes,\n            pageInfo: result.productCategories.pageInfo\n        };\n    } catch (error) {\n        console.error(\"Error fetching categories:\", error);\n        return {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    }\n}\n/**\n * Create a new cart by adding the first item - WooCommerce automatically creates cart\n */ async function createCart() {\n    let items = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];\n    try {\n        if (items.length === 0) {\n            // Just return an empty cart structure - WooCommerce will create cart when first item is added\n            return {\n                contents: {\n                    nodes: []\n                },\n                subtotal: \"0\",\n                total: \"0\",\n                totalTax: \"0\",\n                isEmpty: true,\n                contentsCount: 0\n            };\n        }\n        // Add the first item to create the cart\n        const firstItem = items[0];\n        const cart = await addToCart(\"\", [\n            firstItem\n        ]);\n        // Add remaining items if any\n        if (items.length > 1) {\n            for(let i = 1; i < items.length; i++){\n                await addToCart(\"\", [\n                    items[i]\n                ]);\n            }\n            // Get the updated cart\n            return await getCart();\n        }\n        return cart;\n    } catch (error) {\n        console.error(\"Error creating cart:\", error);\n        throw error;\n    }\n}\n/**\n * Get cart contents - Updated for current WooGraphQL schema\n */ async function getCart() {\n    try {\n        const data = await wooGraphQLFetch({\n            query: GET_CART,\n            variables: {} // Cart query doesn't need parameters in current WooGraphQL\n        });\n        return (data === null || data === void 0 ? void 0 : data.cart) || null;\n    } catch (error) {\n        console.error(\"Error fetching cart:\", error);\n        return null;\n    }\n}\n/**\n * Add items to cart - Updated for current WooGraphQL schema\n */ async function addToCart(_cartId, items) {\n    try {\n        // WooCommerce GraphQL addToCart only accepts one item at a time\n        // So we'll add the first item and return the cart\n        if (items.length === 0) {\n            throw new Error(\"No items provided to add to cart\");\n        }\n        const item = items[0];\n        const variables = {\n            productId: parseInt(item.productId),\n            quantity: item.quantity || 1,\n            variationId: item.variationId ? parseInt(item.variationId) : null,\n            extraData: null\n        };\n        console.log(\"Adding to cart with variables:\", variables);\n        const response = await wooGraphQLFetch({\n            query: ADD_TO_CART,\n            variables\n        });\n        console.log(\"Add to cart response:\", response);\n        return response.addToCart.cart;\n    } catch (error) {\n        console.error(\"Error adding items to cart:\", error);\n        throw error;\n    }\n}\n/**\n * Remove items from cart - Updated for current WooGraphQL schema\n */ async function removeFromCart(cartId, keys) {\n    try {\n        var _data_removeItemsFromCart;\n        const data = await wooGraphQLFetch({\n            query: MUTATION_REMOVE_FROM_CART,\n            variables: {\n                keys,\n                all: false\n            }\n        });\n        return (data === null || data === void 0 ? void 0 : (_data_removeItemsFromCart = data.removeItemsFromCart) === null || _data_removeItemsFromCart === void 0 ? void 0 : _data_removeItemsFromCart.cart) || null;\n    } catch (error) {\n        console.error(\"Error removing items from cart:\", error);\n        throw error;\n    }\n}\n/**\n * Customer login with WooCommerce GraphQL\n * \n * @param username User's email/username\n * @param password User's password\n * @returns Authentication token and user information\n */ async function customerLogin(username, password) {\n    try {\n        const LOGIN_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject17());\n        const variables = {\n            username,\n            password\n        };\n        const result = await wooGraphQLFetch({\n            query: LOGIN_MUTATION,\n            variables\n        });\n        if (!result || !result.login || !result.login.authToken) {\n            throw new Error(\"Login failed: Invalid response from server\");\n        }\n        // Set the auth token for future requests\n        setAuthToken(result.login.authToken);\n        return {\n            authToken: result.login.authToken,\n            refreshToken: result.login.refreshToken,\n            user: result.login.user,\n            customerUserErrors: [] // For compatibility with Shopify auth\n        };\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        // Format the error to match the expected structure\n        return {\n            authToken: null,\n            refreshToken: null,\n            user: null,\n            customerUserErrors: [\n                {\n                    code: \"LOGIN_FAILED\",\n                    message: error.message || \"Login failed. Please check your credentials.\"\n                }\n            ]\n        };\n    }\n}\n/**\n * Create customer (register) with WooCommerce GraphQL\n */ async function createCustomer(param) {\n    let { email, password, firstName, lastName, phone, acceptsMarketing = false } = param;\n    try {\n        const REGISTER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject18());\n        const variables = {\n            input: {\n                clientMutationId: \"registerCustomer\",\n                email,\n                password,\n                firstName,\n                lastName,\n                username: email\n            }\n        };\n        const result = await wooGraphQLFetch({\n            query: REGISTER_MUTATION,\n            variables\n        });\n        if (!result || !result.registerCustomer) {\n            throw new Error(\"Registration failed: Invalid response from server\");\n        }\n        return {\n            customer: result.registerCustomer.customer,\n            authToken: result.registerCustomer.authToken,\n            refreshToken: result.registerCustomer.refreshToken,\n            customerUserErrors: [] // For compatibility with Shopify auth\n        };\n    } catch (error) {\n        console.error(\"Registration error:\", error);\n        // Format the error to match the expected structure\n        return {\n            customer: null,\n            authToken: null,\n            refreshToken: null,\n            customerUserErrors: [\n                {\n                    code: \"REGISTRATION_FAILED\",\n                    message: error.message || \"Registration failed. Please try again.\"\n                }\n            ]\n        };\n    }\n}\n/**\n * Get customer data using JWT authentication\n * \n * @param token JWT auth token\n * @returns Customer data\n */ async function getCustomer(token) {\n    try {\n        if (token) {\n            setAuthToken(token);\n        }\n        const GET_CUSTOMER_QUERY = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject19());\n        const result = await wooGraphQLFetch({\n            query: GET_CUSTOMER_QUERY\n        });\n        if (!result || !result.customer) {\n            throw new Error(\"Failed to get customer data\");\n        }\n        return result.customer;\n    } catch (error) {\n        console.error(\"Error getting customer data:\", error);\n        throw error;\n    } finally{\n        if (token) {\n            clearAuthToken();\n        }\n    }\n}\n/**\n * Normalize product data to match the existing frontend structure\n * This helps maintain compatibility with the existing components\n */ function normalizeProduct(product) {\n    var _product_variations_nodes, _product_variations, _product_variations_nodes1, _product_variations1, _product_variations_nodes2, _product_variations2, _product_attributes_nodes, _product_attributes, _product_productCategories_nodes, _product_productCategories;\n    if (!product) return null;\n    // Extract product type\n    const isVariable = Boolean((_product_variations = product.variations) === null || _product_variations === void 0 ? void 0 : (_product_variations_nodes = _product_variations.nodes) === null || _product_variations_nodes === void 0 ? void 0 : _product_variations_nodes.length);\n    // Extract pricing data\n    let priceRange = {\n        minVariantPrice: {\n            amount: product.price || \"0\",\n            currencyCode: \"INR\" // Default currency for the application\n        },\n        maxVariantPrice: {\n            amount: product.price || \"0\",\n            currencyCode: \"INR\"\n        }\n    };\n    // For variable products, calculate actual price range\n    if (isVariable && ((_product_variations1 = product.variations) === null || _product_variations1 === void 0 ? void 0 : (_product_variations_nodes1 = _product_variations1.nodes) === null || _product_variations_nodes1 === void 0 ? void 0 : _product_variations_nodes1.length) > 0) {\n        const prices = product.variations.nodes.map((variant)=>parseFloat(variant.price || \"0\")).filter((price)=>!isNaN(price));\n        if (prices.length > 0) {\n            priceRange = {\n                minVariantPrice: {\n                    amount: String(Math.min(...prices)),\n                    currencyCode: \"INR\"\n                },\n                maxVariantPrice: {\n                    amount: String(Math.max(...prices)),\n                    currencyCode: \"INR\"\n                }\n            };\n        }\n    }\n    // Extract and normalize images\n    const images = normalizeProductImages(product);\n    // Extract variant data\n    const variants = ((_product_variations2 = product.variations) === null || _product_variations2 === void 0 ? void 0 : (_product_variations_nodes2 = _product_variations2.nodes) === null || _product_variations_nodes2 === void 0 ? void 0 : _product_variations_nodes2.map((variant)=>{\n        var _variant_attributes_nodes, _variant_attributes;\n        return {\n            id: variant.id,\n            title: variant.name,\n            price: {\n                amount: variant.price || \"0\",\n                currencyCode: \"USD\"\n            },\n            availableForSale: variant.stockStatus === \"IN_STOCK\",\n            selectedOptions: ((_variant_attributes = variant.attributes) === null || _variant_attributes === void 0 ? void 0 : (_variant_attributes_nodes = _variant_attributes.nodes) === null || _variant_attributes_nodes === void 0 ? void 0 : _variant_attributes_nodes.map((attr)=>({\n                    name: attr.name,\n                    value: attr.value\n                }))) || [],\n            sku: variant.sku || \"\",\n            image: variant.image ? {\n                url: variant.image.sourceUrl,\n                altText: variant.image.altText || \"\"\n            } : null\n        };\n    })) || [];\n    // Extract options data for variable products\n    const options = ((_product_attributes = product.attributes) === null || _product_attributes === void 0 ? void 0 : (_product_attributes_nodes = _product_attributes.nodes) === null || _product_attributes_nodes === void 0 ? void 0 : _product_attributes_nodes.map((attr)=>({\n            name: attr.name,\n            values: attr.options || []\n        }))) || [];\n    // Extract category data\n    const collections = ((_product_productCategories = product.productCategories) === null || _product_productCategories === void 0 ? void 0 : (_product_productCategories_nodes = _product_productCategories.nodes) === null || _product_productCategories_nodes === void 0 ? void 0 : _product_productCategories_nodes.map((category)=>({\n            handle: category.slug,\n            title: category.name\n        }))) || [];\n    // Extract meta fields for custom data\n    const metafields = {};\n    if (product.metafields) {\n        product.metafields.forEach((meta)=>{\n            metafields[meta.key] = meta.value;\n        });\n    }\n    // Return normalized product object that matches existing frontend structure\n    return {\n        id: product.id,\n        handle: product.slug,\n        title: product.name,\n        description: product.description || \"\",\n        descriptionHtml: product.description || \"\",\n        priceRange,\n        options,\n        variants,\n        images,\n        collections,\n        availableForSale: product.stockStatus !== \"OUT_OF_STOCK\",\n        metafields,\n        // Add original data for reference if needed\n        _originalWooProduct: product\n    };\n}\n/**\n * Normalize product images array\n */ function normalizeProductImages(product) {\n    var _product_galleryImages_nodes, _product_galleryImages;\n    const images = [];\n    // Add main product image if available\n    if (product.image) {\n        images.push({\n            url: product.image.sourceUrl,\n            altText: product.image.altText || product.name || \"\"\n        });\n    }\n    // Add gallery images if available\n    if ((_product_galleryImages = product.galleryImages) === null || _product_galleryImages === void 0 ? void 0 : (_product_galleryImages_nodes = _product_galleryImages.nodes) === null || _product_galleryImages_nodes === void 0 ? void 0 : _product_galleryImages_nodes.length) {\n        product.galleryImages.nodes.forEach((img)=>{\n            // Avoid duplicating the main image if it's already in the gallery\n            const isMainImage = product.image && img.sourceUrl === product.image.sourceUrl;\n            if (!isMainImage) {\n                images.push({\n                    url: img.sourceUrl,\n                    altText: img.altText || product.name || \"\"\n                });\n            }\n        });\n    }\n    return images;\n}\n/**\n * Normalize category data to match existing frontend structure\n */ function normalizeCategory(category) {\n    var _category_products_nodes, _category_products;\n    if (!category) return null;\n    return {\n        id: category.id,\n        handle: category.slug,\n        title: category.name,\n        description: category.description || \"\",\n        image: category.image ? {\n            url: category.image.sourceUrl,\n            altText: category.image.altText || \"\"\n        } : null,\n        products: ((_category_products = category.products) === null || _category_products === void 0 ? void 0 : (_category_products_nodes = _category_products.nodes) === null || _category_products_nodes === void 0 ? void 0 : _category_products_nodes.map(normalizeProduct)) || []\n    };\n}\n/**\n * Get custom meta field from product\n */ const getMetafield = function(product, key, namespace) {\n    let defaultValue = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : \"\";\n    if (!product || !product.metafields) return defaultValue;\n    // Find the meta field by key\n    if (namespace) {\n        const metaKey = \"\".concat(namespace, \":\").concat(key);\n        return product.metafields[metaKey] || defaultValue;\n    }\n    return product.metafields[key] || defaultValue;\n};\n/**\n * Normalize cart data to match existing frontend structure\n */ function normalizeCart(cart) {\n    var _cart_contents_nodes, _cart_contents, _cart_appliedCoupons_nodes, _cart_appliedCoupons;\n    if (!cart) return null;\n    const lineItems = ((_cart_contents = cart.contents) === null || _cart_contents === void 0 ? void 0 : (_cart_contents_nodes = _cart_contents.nodes) === null || _cart_contents_nodes === void 0 ? void 0 : _cart_contents_nodes.map((item)=>{\n        var _item_product, _item_variation, _variation_attributes_nodes, _variation_attributes;\n        const product = (_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.node;\n        const variation = (_item_variation = item.variation) === null || _item_variation === void 0 ? void 0 : _item_variation.node;\n        return {\n            id: item.key,\n            quantity: item.quantity,\n            merchandise: {\n                id: (variation === null || variation === void 0 ? void 0 : variation.id) || (product === null || product === void 0 ? void 0 : product.id),\n                title: (variation === null || variation === void 0 ? void 0 : variation.name) || (product === null || product === void 0 ? void 0 : product.name),\n                product: {\n                    id: product === null || product === void 0 ? void 0 : product.id,\n                    handle: product === null || product === void 0 ? void 0 : product.slug,\n                    title: product === null || product === void 0 ? void 0 : product.name,\n                    image: (product === null || product === void 0 ? void 0 : product.image) ? {\n                        url: product === null || product === void 0 ? void 0 : product.image.sourceUrl,\n                        altText: (product === null || product === void 0 ? void 0 : product.image.altText) || \"\"\n                    } : null\n                },\n                selectedOptions: (variation === null || variation === void 0 ? void 0 : (_variation_attributes = variation.attributes) === null || _variation_attributes === void 0 ? void 0 : (_variation_attributes_nodes = _variation_attributes.nodes) === null || _variation_attributes_nodes === void 0 ? void 0 : _variation_attributes_nodes.map((attr)=>({\n                        name: attr.name,\n                        value: attr.value\n                    }))) || []\n            },\n            cost: {\n                totalAmount: {\n                    amount: item.total || \"0\",\n                    currencyCode: \"USD\"\n                }\n            }\n        };\n    })) || [];\n    const discountCodes = ((_cart_appliedCoupons = cart.appliedCoupons) === null || _cart_appliedCoupons === void 0 ? void 0 : (_cart_appliedCoupons_nodes = _cart_appliedCoupons.nodes) === null || _cart_appliedCoupons_nodes === void 0 ? void 0 : _cart_appliedCoupons_nodes.map((coupon)=>({\n            code: coupon.code,\n            amount: coupon.discountAmount || \"0\"\n        }))) || [];\n    // Calculate total quantity from line items instead of using contentsCount\n    const totalQuantity = lineItems.reduce((sum, item)=>sum + item.quantity, 0);\n    return {\n        id: cart.id,\n        checkoutUrl: \"\",\n        totalQuantity: totalQuantity,\n        cost: {\n            subtotalAmount: {\n                amount: cart.subtotal || \"0\",\n                currencyCode: \"USD\"\n            },\n            totalAmount: {\n                amount: cart.total || \"0\",\n                currencyCode: \"USD\"\n            }\n        },\n        lines: lineItems,\n        discountCodes\n    };\n}\n/**\n * Generates a checkout URL for WooCommerce\n * \n * @param cartId The cart ID to associate with checkout\n * @param isLoggedIn Whether the user is logged in\n * @returns The WooCommerce checkout URL\n */ function getWooCommerceCheckoutUrl(cartId) {\n    let isLoggedIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n    // Base checkout URL\n    const baseUrl = \"\".concat(wooConfig.storeUrl, \"/checkout\");\n    // Add cart parameter if needed\n    const cartParam = cartId ? \"?cart=\".concat(cartId) : \"\";\n    // Add comprehensive guest checkout parameters to ensure login is bypassed\n    // These parameters will work across different WooCommerce configurations and plugins\n    let guestParams = \"\";\n    if (!isLoggedIn) {\n        const separator = cartParam ? \"&\" : \"?\";\n        guestParams = \"\".concat(separator, \"guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0\");\n    }\n    // Construct the full URL\n    return \"\".concat(baseUrl).concat(cartParam).concat(guestParams);\n}\n/**\n * Get a product by ID with cache tags for efficient revalidation\n * \n * @param id The product ID\n * @param revalidate Revalidation period in seconds (optional)\n * @returns The product data\n */ async function getProductById(id) {\n    let revalidate = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 60;\n    try {\n        // Define cache tags for this product\n        const tags = [\n            \"product-\".concat(id),\n            \"products\",\n            \"inventory\"\n        ];\n        // Define the query\n        const QUERY_PRODUCT_BY_ID = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject20());\n        // Fetch the product with tags\n        const data = await fetchFromWooCommerce(QUERY_PRODUCT_BY_ID, {\n            id\n        }, tags, revalidate);\n        return (data === null || data === void 0 ? void 0 : data.product) || null;\n    } catch (error) {\n        console.error(\"Error fetching product with ID \".concat(id, \":\"), error);\n        throw error;\n    }\n}\n/**\n * Search products by keyword with advanced options\n * @param query Search query\n * @param options Search options including pagination, sorting, filtering\n * @returns Products matching the search query\n */ async function searchProducts(query) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    // Handle case where options is passed as a number for backward compatibility\n    const first = typeof options === \"number\" ? options : options.first || 10;\n    const searchQuery = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject21());\n    try {\n        const data = await graphQLClient.request(searchQuery, {\n            query,\n            first\n        });\n        return (data === null || data === void 0 ? void 0 : data.products) || {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    } catch (error) {\n        console.error(\"Error searching products:\", error);\n        return {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    }\n}\n/**\n * Get a single product by ID\n * @param id Product ID\n * @returns Product data\n */ async function getProduct(id) {\n    const productQuery = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject22());\n    try {\n        const data = await graphQLClient.request(productQuery, {\n            id\n        });\n        return data.product;\n    } catch (error) {\n        console.error(\"Error fetching product:\", error);\n        throw new Error(\"Failed to fetch product\");\n    }\n}\nasync function getCategoryProducts(slug) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    try {\n        const { first = 20 } = options;\n        const data = await graphQLClient.request(QUERY_CATEGORY_PRODUCTS, {\n            slug,\n            first\n        });\n        return (data === null || data === void 0 ? void 0 : data.productCategory) || null;\n    } catch (error) {\n        console.error(\"Error fetching category products with slug \".concat(slug, \":\"), error);\n        return null;\n    }\n}\n// Customer Mutations\nconst CREATE_CUSTOMER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject23());\nconst UPDATE_CUSTOMER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject24());\nconst GET_CUSTOMER_QUERY = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject25());\nconst CREATE_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject26());\nconst UPDATE_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject27());\nconst DELETE_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject28());\nconst SET_DEFAULT_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject29());\nconst UPDATE_CART_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject30());\n/**\n * Update customer profile\n */ async function updateCustomer(token, customerData) {\n    try {\n        console.log(\"Updating customer with data:\", customerData);\n        console.log(\"Using token:\", token ? \"Token present\" : \"No token\");\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        const variables = {\n            input: {\n                clientMutationId: \"updateCustomer\",\n                ...customerData\n            }\n        };\n        console.log(\"GraphQL variables:\", variables);\n        const response = await client.request(UPDATE_CUSTOMER_MUTATION, variables);\n        console.log(\"GraphQL response:\", response);\n        if (response.updateCustomer.customerUserErrors && response.updateCustomer.customerUserErrors.length > 0) {\n            const errorMessage = response.updateCustomer.customerUserErrors[0].message;\n            console.error(\"Customer update error:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        return response.updateCustomer;\n    } catch (error) {\n        console.error(\"Error updating customer:\", error);\n        throw error;\n    }\n}\n/**\n * Create a new address for the customer\n */ async function createAddress(token, address) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        // Determine if this is a billing or shipping address\n        const addressType = address.addressType || \"shipping\";\n        const variables = {\n            input: {\n                [\"\".concat(addressType)]: {\n                    firstName: address.firstName || \"\",\n                    lastName: address.lastName || \"\",\n                    company: address.company || \"\",\n                    address1: address.address1 || \"\",\n                    address2: address.address2 || \"\",\n                    city: address.city || \"\",\n                    state: address.province || \"\",\n                    postcode: address.zip || \"\",\n                    country: address.country || \"\",\n                    ...addressType === \"billing\" ? {\n                        email: address.email || \"\",\n                        phone: address.phone || \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(CREATE_ADDRESS_MUTATION, variables);\n        return {\n            customerAddress: response.updateCustomer.customer[addressType],\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error creating address:\", error);\n        throw error;\n    }\n}\n/**\n * Update an existing address\n */ async function updateAddress(token, id, address) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        // Determine if this is a billing or shipping address\n        const addressType = address.addressType || \"shipping\";\n        const variables = {\n            input: {\n                [\"\".concat(addressType)]: {\n                    firstName: address.firstName || \"\",\n                    lastName: address.lastName || \"\",\n                    company: address.company || \"\",\n                    address1: address.address1 || \"\",\n                    address2: address.address2 || \"\",\n                    city: address.city || \"\",\n                    state: address.province || \"\",\n                    postcode: address.zip || \"\",\n                    country: address.country || \"\",\n                    ...addressType === \"billing\" ? {\n                        email: address.email || \"\",\n                        phone: address.phone || \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(UPDATE_ADDRESS_MUTATION, variables);\n        return {\n            customerAddress: response.updateCustomer.customer[addressType],\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error updating address:\", error);\n        throw error;\n    }\n}\n/**\n * Delete an address\n * Note: In WooCommerce, we don't actually delete addresses but clear them\n */ async function deleteAddress(token, id) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        // Get the current customer to determine which address to clear\n        const customer = await getCustomer(token);\n        // Determine if this is a billing or shipping address\n        // In this implementation, we're using the id to determine which address to clear\n        // This is a simplification - you might need a different approach\n        const addressType = id === \"billing\" ? \"billing\" : \"shipping\";\n        const variables = {\n            input: {\n                [\"\".concat(addressType)]: {\n                    firstName: \"\",\n                    lastName: \"\",\n                    company: \"\",\n                    address1: \"\",\n                    address2: \"\",\n                    city: \"\",\n                    state: \"\",\n                    postcode: \"\",\n                    country: \"\",\n                    ...addressType === \"billing\" ? {\n                        email: customer.email || \"\",\n                        phone: \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(DELETE_ADDRESS_MUTATION, variables);\n        return {\n            deletedCustomerAddressId: id,\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error deleting address:\", error);\n        throw error;\n    }\n}\n/**\n * Set default address\n * Note: In WooCommerce, the concept of \"default\" address is different\n * This implementation copies the address from one type to another\n */ async function setDefaultAddress(token, addressId) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        // Get the current customer\n        const customer = await getCustomer(token);\n        // Determine source and target address types\n        // This is a simplification - you might need a different approach\n        const sourceType = addressId === \"billing\" ? \"billing\" : \"shipping\";\n        const targetType = sourceType === \"billing\" ? \"shipping\" : \"billing\";\n        // Copy the address from source to target\n        const sourceAddress = customer[sourceType];\n        const variables = {\n            input: {\n                [\"\".concat(targetType)]: {\n                    ...sourceAddress,\n                    ...targetType === \"billing\" ? {\n                        email: customer.email || \"\",\n                        phone: sourceAddress.phone || \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(SET_DEFAULT_ADDRESS_MUTATION, variables);\n        return {\n            customer: response.updateCustomer.customer,\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error setting default address:\", error);\n        throw error;\n    }\n}\n/**\n * Update cart items\n */ async function updateCart(items) {\n    try {\n        const variables = {\n            input: {\n                items\n            }\n        };\n        const response = await wooGraphQLFetch({\n            query: UPDATE_CART_MUTATION,\n            variables\n        });\n        return response.updateItemQuantities.cart;\n    } catch (error) {\n        console.error(\"Error updating cart:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/woocommerce.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["framework-node_modules_next_dist_a","framework-node_modules_next_dist_client_a","framework-node_modules_next_dist_client_components_ap","framework-node_modules_next_dist_client_components_b","framework-node_modules_next_dist_client_components_layout-router_js-4906aef6","framework-node_modules_next_dist_client_components_m","framework-node_modules_next_dist_client_components_p","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_C","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_LeftRightDi-d5fdd2e0","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_O","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_Overlay_mai-e776ae3b","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_Te","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_V","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_container_B","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_container_R","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_f","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_h","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_h","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_styles_B","framework-node_modules_next_dist_client_components_rea","framework-node_modules_next_dist_client_components_re","framework-node_modules_next_dist_client_components_router-reducer_co","framework-node_modules_next_dist_client_components_router-reducer_fe","framework-node_modules_next_dist_client_components_router-reducer_h","framework-node_modules_next_dist_client_components_router-reducer_pp","framework-node_modules_next_dist_client_components_router-reducer_reducers_f","framework-node_modules_next_dist_client_components_router-reducer_reducers_r","framework-node_modules_next_dist_client_components_router-reducer_r","framework-node_modules_next_dist_client_c","framework-node_modules_next_dist_client_g","framework-node_modules_next_dist_client_l","framework-node_modules_next_dist_compiled_a","framework-node_modules_next_dist_compiled_m","framework-node_modules_next_dist_compiled_react-dom_cjs_react-dom_development_js-3041f41d","framework-node_modules_next_dist_compiled_react-d","framework-node_modules_next_dist_compiled_react-server-dom-webpack_cjs_react-server-dom-webpack-clie-4912d8da","framework-node_modules_next_dist_compiled_react_cjs_react-jsx-dev-runtime_development_js-12999a20","framework-node_modules_next_dist_compiled_react_c","framework-node_modules_next_dist_compiled_react_cjs_react_development_js-a784779d","framework-node_modules_next_dist_compiled_r","framework-node_modules_next_dist_l","framework-node_modules_next_dist_shared_lib_a","framework-node_modules_next_dist_shared_lib_ha","framework-node_modules_next_dist_shared_lib_h","framework-node_modules_next_dist_shared_lib_lazy-dynamic_b","framework-node_modules_next_dist_shared_lib_m","framework-node_modules_next_dist_shared_lib_router-","framework-node_modules_next_dist_shared_lib_router_utils_o","framework-node_modules_next_dist_shared_lib_r","framework-node_modules_next_d","framework-node_modules_next_font_google_target_css-0","commons","vendors-_app-pages-browser_node_modules_crypto-js_enc-hex_js-_app-pages-browser_node_modules_-7fd5da","vendors-_app-pages-browser_node_modules_formspree_react_dist_index_mjs-_app-pages-browser_nod-6fc5c0","vendors-_app-pages-browser_node_modules_framer-motion_dist_es_animation_generators_inertia_mj-aa7637","vendors-_app-pages-browser_node_modules_framer-motion_dist_es_debug_record_mjs-_app-pages-bro-e51582","vendors-_app-pages-browser_node_modules_framer-motion_dist_es_motion_features_animations_mjs--fc10a8","vendors-_app-pages-browser_node_modules_framer-motion_dist_es_projection_animation_mix-values-de30e4","vendors-_app-pages-browser_node_modules_framer-motion_dist_es_projection_node_create-projecti-f7e991","vendors-_app-pages-browser_node_modules_framer-motion_dist_es_render_VisualElement_mjs","vendors-_app-pages-browser_node_modules_framer-motion_dist_es_render_dom_motion_mjs-_app-page-dae8e4","vendors-_app-pages-browser_node_modules_framer-motion_dist_es_render_svg_SVGVisualElement_mjs-181899","vendors-_app-pages-browser_node_modules_framer-motion_dist_es_value_index_mjs-_app-pages-brow-194847","vendors-_app-pages-browser_node_modules_goober_dist_goober_modern_js-_app-pages-browser_node_-a9bda9","vendors-_app-pages-browser_node_modules_graphql_language_directiveLocation_mjs-_app-pages-bro-9dcc35","vendors-_app-pages-browser_node_modules_graphql_language_parser_mjs","vendors-_app-pages-browser_node_modules_graphql_language_printLocation_mjs-_app-pages-browser-e8602f","vendors-_app-pages-browser_node_modules_lucide-react_dist_esm_icons_alert-circle_js-_app-page-e3f982","vendors-_app-pages-browser_node_modules_stripe_react-stripe-js_dist_react-stripe_umd_js","vendors-_app-pages-browser_node_modules_stripe_stripe-js_pure_index_js-_app-pages-browser_nod-cf7a0b","vendors-_app-pages-browser_node_modules_tailwind-merge_dist_bundle-mjs_mjs","vendors-_app-pages-browser_node_modules_upstash_redis_chunk-5XANP4AV_mjs","app/page-_","app/page-src_app_page_tsx-8a0e3ed7","app/page-src_components_home_B","app/page-src_components_product_ProductCard_tsx-64157a56","app/page-src_components_p","app/page-src_c","app/page-src_lib_a","app/page-src_lib_i","app/page-src_lib_s","main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);
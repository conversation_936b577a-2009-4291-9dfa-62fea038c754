/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["vendors-_app-pages-browser_node_modules_formspree_react_dist_index_mjs-_app-pages-browser_nod-6fc5c0"],{

/***/ "(app-pages-browser)/./node_modules/@formspree/core/dist/index.js":
/*!****************************************************!*\
  !*** ./node_modules/@formspree/core/dist/index.js ***!
  \****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var g=Object.defineProperty;var j=Object.getOwnPropertyDescriptor;var V=Object.getOwnPropertyNames;var L=Object.prototype.hasOwnProperty;var Y=(e,r)=>{for(var t in r)g(e,t,{get:r[t],enumerable:!0})},K=(e,r,t,o)=>{if(r&&typeof r==\"object\"||typeof r==\"function\")for(let s of V(r))!L.call(e,s)&&s!==t&&g(e,s,{get:()=>r[s],enumerable:!(o=j(r,s))||o.enumerable});return e};var $=e=>K(g({},\"__esModule\",{value:!0}),e);var h=(e,r,t)=>new Promise((o,s)=>{var i=a=>{try{l(t.next(a))}catch(m){s(m)}},c=a=>{try{l(t.throw(a))}catch(m){s(m)}},l=a=>a.done?o(a.value):Promise.resolve(a.value).then(i,c);l((t=t.apply(e,r)).next())});var W={};Y(W,{SubmissionError:()=>p,appendExtraData:()=>E,createClient:()=>R,getDefaultClient:()=>U,isSubmissionError:()=>A});module.exports=$(W);var u=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\",J=/^(?:[A-Za-z\\d+\\/]{4})*?(?:[A-Za-z\\d+\\/]{2}(?:==)?|[A-Za-z\\d+\\/]{3}=?)?$/;function I(e){e=String(e);for(var r,t,o,s,i=\"\",c=0,l=e.length%3;c<e.length;){if((t=e.charCodeAt(c++))>255||(o=e.charCodeAt(c++))>255||(s=e.charCodeAt(c++))>255)throw new TypeError(\"Failed to execute 'btoa' on 'Window': The string to be encoded contains characters outside of the Latin1 range.\");r=t<<16|o<<8|s,i+=u.charAt(r>>18&63)+u.charAt(r>>12&63)+u.charAt(r>>6&63)+u.charAt(r&63)}return l?i.slice(0,l-3)+\"===\".substring(l):i}function O(e){if(e=String(e).replace(/[\\t\\n\\f\\r ]+/g,\"\"),!J.test(e))throw new TypeError(\"Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.\");e+=\"==\".slice(2-(e.length&3));for(var r,t=\"\",o,s,i=0;i<e.length;)r=u.indexOf(e.charAt(i++))<<18|u.indexOf(e.charAt(i++))<<12|(o=u.indexOf(e.charAt(i++)))<<6|(s=u.indexOf(e.charAt(i++))),t+=o===64?String.fromCharCode(r>>16&255):s===64?String.fromCharCode(r>>16&255,r>>8&255):String.fromCharCode(r>>16&255,r>>8&255,r&255);return t}var G=()=>navigator.webdriver||!!document.documentElement.getAttribute(O(\"d2ViZHJpdmVy\"))||!!window.callPhantom||!!window._phantom,y=class{constructor(){this.loadedAt=Date.now(),this.webdriver=G()}data(){return{loadedAt:this.loadedAt,webdriver:this.webdriver}}};var S=class{constructor(r){this.kind=\"success\";this.next=r.next}};function w(e){return\"next\"in e&&typeof e.next==\"string\"}var b=class{constructor(r,t){this.paymentIntentClientSecret=r;this.resubmitKey=t;this.kind=\"stripePluginPending\"}};function _(e){if(\"stripe\"in e&&\"resubmitKey\"in e&&typeof e.resubmitKey==\"string\"){let{stripe:r}=e;return typeof r==\"object\"&&r!=null&&\"paymentIntentClientSecret\"in r&&typeof r.paymentIntentClientSecret==\"string\"}return!1}function A(e){return e.kind===\"error\"}var p=class{constructor(...r){this.kind=\"error\";this.formErrors=[];this.fieldErrors=new Map;var t;for(let o of r){if(!o.field){this.formErrors.push({code:o.code&&z(o.code)?o.code:\"UNSPECIFIED\",message:o.message});continue}let s=(t=this.fieldErrors.get(o.field))!=null?t:[];s.push({code:o.code&&Q(o.code)?o.code:\"UNSPECIFIED\",message:o.message}),this.fieldErrors.set(o.field,s)}}getFormErrors(){return[...this.formErrors]}getFieldErrors(r){var t;return(t=this.fieldErrors.get(r))!=null?t:[]}getAllFieldErrors(){return Array.from(this.fieldErrors)}};function z(e){return e in B}var B={BLOCKED:\"BLOCKED\",EMPTY:\"EMPTY\",FILES_TOO_BIG:\"FILES_TOO_BIG\",FORM_NOT_FOUND:\"FORM_NOT_FOUND\",INACTIVE:\"INACTIVE\",NO_FILE_UPLOADS:\"NO_FILE_UPLOADS\",PROJECT_NOT_FOUND:\"PROJECT_NOT_FOUND\",TOO_MANY_FILES:\"TOO_MANY_FILES\"};function Q(e){return e in Z}var Z={REQUIRED_FIELD_EMPTY:\"REQUIRED_FIELD_EMPTY\",REQUIRED_FIELD_MISSING:\"REQUIRED_FIELD_MISSING\",STRIPE_CLIENT_ERROR:\"STRIPE_CLIENT_ERROR\",STRIPE_SCA_ERROR:\"STRIPE_SCA_ERROR\",TYPE_EMAIL:\"TYPE_EMAIL\",TYPE_NUMERIC:\"TYPE_NUMERIC\",TYPE_TEXT:\"TYPE_TEXT\"};function P(e){return\"errors\"in e&&Array.isArray(e.errors)&&e.errors.every(r=>typeof r.message==\"string\")||\"error\"in e&&typeof e.error==\"string\"}var D=\"4.0.0\";var v=e=>I(JSON.stringify(e)),N=e=>{let r=`@formspree/core@${D}`;return e?`${e} ${r}`:r};function E(e,r,t){e instanceof FormData?e.append(r,t):e[r]=t}function M(e){return e!==null&&typeof e==\"object\"}var C=class{constructor(r={}){this.project=r.project,this.stripe=r.stripe,typeof window!=\"undefined\"&&(this.session=new y)}submitForm(s,i){return h(this,arguments,function*(r,t,o={}){let c=o.endpoint||\"https://formspree.io\",l=this.project?`${c}/p/${this.project}/f/${r}`:`${c}/f/${r}`,a={Accept:\"application/json\",\"Formspree-Client\":N(o.clientName)};this.session&&(a[\"Formspree-Session-Data\"]=v(this.session.data())),t instanceof FormData||(a[\"Content-Type\"]=\"application/json\");function m(f){return h(this,null,function*(){try{let n=yield(yield fetch(l,{method:\"POST\",mode:\"cors\",body:f instanceof FormData?f:JSON.stringify(f),headers:a})).json();if(M(n)){if(P(n))return Array.isArray(n.errors)?new p(...n.errors):new p({message:n.error});if(_(n))return new b(n.stripe.paymentIntentClientSecret,n.resubmitKey);if(w(n))return new S({next:n.next})}return new p({message:\"Unexpected response format\"})}catch(d){let n=d instanceof Error?d.message:`Unknown error while posting to Formspree: ${JSON.stringify(d)}`;return new p({message:n})}})}if(this.stripe&&o.createPaymentMethod){let f=yield o.createPaymentMethod();if(f.error)return new p({code:\"STRIPE_CLIENT_ERROR\",field:\"paymentMethod\",message:\"Error creating payment method\"});E(t,\"paymentMethod\",f.paymentMethod.id);let d=yield m(t);if(d.kind===\"error\")return d;if(d.kind===\"stripePluginPending\"){let n=yield this.stripe.handleCardAction(d.paymentIntentClientSecret);if(n.error)return new p({code:\"STRIPE_CLIENT_ERROR\",field:\"paymentMethod\",message:\"Stripe SCA error\"});t instanceof FormData?t.delete(\"paymentMethod\"):delete t.paymentMethod,E(t,\"paymentIntent\",n.paymentIntent.id),E(t,\"resubmitKey\",d.resubmitKey);let x=yield m(t);return k(x),x}return d}let T=yield m(t);return k(T),T})}};function k(e){let{kind:r}=e;if(r!==\"success\"&&r!==\"error\")throw new Error(`Unexpected submission result (kind: ${r})`)}var R=e=>new C(e),U=()=>(F||(F=R()),F),F;0&&(0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formspree/core/dist/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formspree/react/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@formspree/react/dist/index.mjs ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CardElement: function() { return /* reexport safe */ _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_0__.CardElement; },\n/* harmony export */   FormspreeProvider: function() { return /* binding */ N; },\n/* harmony export */   ValidationError: function() { return /* binding */ V; },\n/* harmony export */   useForm: function() { return /* binding */ J; },\n/* harmony export */   useFormspree: function() { return /* binding */ b; },\n/* harmony export */   useSubmit: function() { return /* binding */ F; }\n/* harmony export */ });\n/* harmony import */ var _stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @stripe/react-stripe-js */ \"(app-pages-browser)/./node_modules/@stripe/react-stripe-js/dist/react-stripe.umd.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _formspree_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @formspree/core */ \"(app-pages-browser)/./node_modules/@formspree/core/dist/index.js\");\n/* harmony import */ var _stripe_stripe_js_pure_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @stripe/stripe-js/pure/index.js */ \"(app-pages-browser)/./node_modules/@stripe/stripe-js/pure/index.js\");\nfunction V(e){let{prefix:t,field:r,errors:o,...s}=e;if(o==null)return null;let n=r?o.getFieldErrors(r):o.getFormErrors();return n.length===0?null:react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\",{...s},t?`${t} `:null,n.map(a=>a.message).join(\", \"))}var E=(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({elements:null});function P(e){let{children:t}=e,r=(0,_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_0__.useElements)();return react__WEBPACK_IMPORTED_MODULE_1__.createElement(E.Provider,{value:{elements:r}},t)}function v(){return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(E)}var h=react__WEBPACK_IMPORTED_MODULE_1__.createContext(null);function N(e){let{children:t,project:r,stripePK:o}=e,[s,n]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((0,_formspree_core__WEBPACK_IMPORTED_MODULE_3__.createClient)({project:r})),a=(0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>o?(0,_stripe_stripe_js_pure_index_js__WEBPACK_IMPORTED_MODULE_2__.loadStripe)(o):null,[o]);return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{let i=!0;return i&&n(l=>l.project!==r?(0,_formspree_core__WEBPACK_IMPORTED_MODULE_3__.createClient)({...l,project:r}):l),()=>{i=!1}},[r]),(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{let i=!0;return a?.then(l=>{i&&l&&n(p=>(0,_formspree_core__WEBPACK_IMPORTED_MODULE_3__.createClient)({...p,stripe:l}))}),()=>{i=!1}},[a]),react__WEBPACK_IMPORTED_MODULE_1__.createElement(h.Provider,{value:{client:s}},a?react__WEBPACK_IMPORTED_MODULE_1__.createElement(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_0__.Elements,{stripe:a},react__WEBPACK_IMPORTED_MODULE_1__.createElement(P,null,t)):t)}function b(){return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(h)??{client:(0,_formspree_core__WEBPACK_IMPORTED_MODULE_3__.getDefaultClient)()}}var D=\"3.0.0\";var z=`@formspree/react@${D}`;function F(e,t={}){let r=b(),{client:o=r.client,extraData:s,origin:n}=t,{elements:a}=v(),{stripe:i}=o;return async function(p){let m=I(p)?$(p):p;if(typeof s==\"object\")for(let[u,g]of Object.entries(s)){let d;typeof g==\"function\"?d=await g():d=g,d!==void 0&&(0,_formspree_core__WEBPACK_IMPORTED_MODULE_3__.appendExtraData)(m,u,d)}let c=a?.getElement(_stripe_react_stripe_js__WEBPACK_IMPORTED_MODULE_0__.CardElement),x=i&&c?()=>i.createPaymentMethod({type:\"card\",card:c,billing_details:G(m)}):void 0;return o.submitForm(e,m,{endpoint:n,clientName:z,createPaymentMethod:x})}}function I(e){return\"preventDefault\"in e&&typeof e.preventDefault==\"function\"}function $(e){e.preventDefault();let t=e.currentTarget;if(t.tagName!=\"FORM\")throw new Error(\"submit was triggered for a non-form element\");return new FormData(t)}function G(e){let t={address:Y(e)};for(let r of[\"name\",\"email\",\"phone\"]){let o=e instanceof FormData?e.get(r):e[r];o&&typeof o==\"string\"&&(t[r]=o)}return t}function Y(e){let t={};for(let[r,o]of[[\"address_line1\",\"line1\"],[\"address_line2\",\"line2\"],[\"address_city\",\"city\"],[\"address_country\",\"country\"],[\"address_state\",\"state\"],[\"address_postal_code\",\"postal_code\"]]){let s=e instanceof FormData?e.get(r):e[r];s&&typeof s==\"string\"&&(t[o]=s)}return t}function J(e,t={}){let[r,o]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null),[s,n]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null),[a,i]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1),[l,p]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1);if(!e)throw new Error('You must provide a form key or hashid (e.g. useForm(\"myForm\") or useForm(\"123xyz\")');let m=F(e,{client:t.client,extraData:t.data,origin:t.endpoint});return[{errors:r,result:s,submitting:a,succeeded:l},async function(x){i(!0);let u=await m(x);i(!1),(0,_formspree_core__WEBPACK_IMPORTED_MODULE_3__.isSubmissionError)(u)?(o(u),p(!1)):(o(null),n(u),p(!0))},function(){o(null),n(null),i(!1),p(!1)}]}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formspree/react/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/instant.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animators/instant.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createInstantAnimation: function() { return /* binding */ createInstantAnimation; }\n/* harmony export */ });\n/* harmony import */ var _js_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./js/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/index.mjs\");\n/* harmony import */ var _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n\n\n\nfunction createInstantAnimation({ keyframes, delay, onUpdate, onComplete, }) {\n    const setValue = () => {\n        onUpdate && onUpdate(keyframes[keyframes.length - 1]);\n        onComplete && onComplete();\n        /**\n         * TODO: As this API grows it could make sense to always return\n         * animateValue. This will be a bigger project as animateValue\n         * is frame-locked whereas this function resolves instantly.\n         * This is a behavioural change and also has ramifications regarding\n         * assumptions within tests.\n         */\n        return {\n            time: 0,\n            speed: 1,\n            duration: 0,\n            play: (_utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop),\n            pause: (_utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop),\n            stop: (_utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop),\n            then: (resolve) => {\n                resolve();\n                return Promise.resolve();\n            },\n            cancel: (_utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop),\n            complete: (_utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop),\n        };\n    };\n    return delay\n        ? (0,_js_index_mjs__WEBPACK_IMPORTED_MODULE_1__.animateValue)({\n            keyframes: [0, 1],\n            duration: 0,\n            delay,\n            onComplete: setValue,\n        })\n        : setValue();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/instant.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/driver-frameloop.mjs":
/*!****************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animators/js/driver-frameloop.mjs ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   frameloopDriver: function() { return /* binding */ frameloopDriver; }\n/* harmony export */ });\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\nconst frameloopDriver = (update) => {\n    const passTimestamp = ({ timestamp }) => update(timestamp);\n    return {\n        start: () => _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame.update(passTimestamp, true),\n        stop: () => (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(passTimestamp),\n        /**\n         * If we're processing this frame we can use the\n         * framelocked timestamp to keep things in sync.\n         */\n        now: () => _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frameData.isProcessing ? _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frameData.timestamp : performance.now(),\n    };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2FuaW1hdG9ycy9qcy9kcml2ZXItZnJhbWVsb29wLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE2RTs7QUFFN0U7QUFDQSw2QkFBNkIsV0FBVztBQUN4QztBQUNBLHFCQUFxQix1REFBSztBQUMxQixvQkFBb0IsaUVBQVc7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsMkRBQVMsZ0JBQWdCLDJEQUFTO0FBQ3JEO0FBQ0E7O0FBRTJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2FuaW1hdG9ycy9qcy9kcml2ZXItZnJhbWVsb29wLm1qcz80OTI0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZyYW1lLCBjYW5jZWxGcmFtZSwgZnJhbWVEYXRhIH0gZnJvbSAnLi4vLi4vLi4vZnJhbWVsb29wL2ZyYW1lLm1qcyc7XG5cbmNvbnN0IGZyYW1lbG9vcERyaXZlciA9ICh1cGRhdGUpID0+IHtcbiAgICBjb25zdCBwYXNzVGltZXN0YW1wID0gKHsgdGltZXN0YW1wIH0pID0+IHVwZGF0ZSh0aW1lc3RhbXApO1xuICAgIHJldHVybiB7XG4gICAgICAgIHN0YXJ0OiAoKSA9PiBmcmFtZS51cGRhdGUocGFzc1RpbWVzdGFtcCwgdHJ1ZSksXG4gICAgICAgIHN0b3A6ICgpID0+IGNhbmNlbEZyYW1lKHBhc3NUaW1lc3RhbXApLFxuICAgICAgICAvKipcbiAgICAgICAgICogSWYgd2UncmUgcHJvY2Vzc2luZyB0aGlzIGZyYW1lIHdlIGNhbiB1c2UgdGhlXG4gICAgICAgICAqIGZyYW1lbG9ja2VkIHRpbWVzdGFtcCB0byBrZWVwIHRoaW5ncyBpbiBzeW5jLlxuICAgICAgICAgKi9cbiAgICAgICAgbm93OiAoKSA9PiBmcmFtZURhdGEuaXNQcm9jZXNzaW5nID8gZnJhbWVEYXRhLnRpbWVzdGFtcCA6IHBlcmZvcm1hbmNlLm5vdygpLFxuICAgIH07XG59O1xuXG5leHBvcnQgeyBmcmFtZWxvb3BEcml2ZXIgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/driver-frameloop.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/index.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animators/js/index.mjs ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateValue: function() { return /* binding */ animateValue; }\n/* harmony export */ });\n/* harmony import */ var _generators_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../generators/keyframes.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/keyframes.mjs\");\n/* harmony import */ var _generators_spring_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../generators/spring/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/spring/index.mjs\");\n/* harmony import */ var _generators_inertia_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../generators/inertia.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/inertia.mjs\");\n/* harmony import */ var _driver_frameloop_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./driver-frameloop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/driver-frameloop.mjs\");\n/* harmony import */ var _utils_interpolate_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/interpolate.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/interpolate.mjs\");\n/* harmony import */ var _utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../utils/clamp.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs\");\n/* harmony import */ var _utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../utils/time-conversion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/time-conversion.mjs\");\n/* harmony import */ var _generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../generators/utils/calc-duration.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/utils/calc-duration.mjs\");\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n\n\n\n\n\n\n\n\n\n\nconst types = {\n    decay: _generators_inertia_mjs__WEBPACK_IMPORTED_MODULE_0__.inertia,\n    inertia: _generators_inertia_mjs__WEBPACK_IMPORTED_MODULE_0__.inertia,\n    tween: _generators_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__.keyframes,\n    keyframes: _generators_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__.keyframes,\n    spring: _generators_spring_index_mjs__WEBPACK_IMPORTED_MODULE_2__.spring,\n};\n/**\n * Animate a single value on the main thread.\n *\n * This function is written, where functionality overlaps,\n * to be largely spec-compliant with WAAPI to allow fungibility\n * between the two.\n */\nfunction animateValue({ autoplay = true, delay = 0, driver = _driver_frameloop_mjs__WEBPACK_IMPORTED_MODULE_3__.frameloopDriver, keyframes: keyframes$1, type = \"keyframes\", repeat = 0, repeatDelay = 0, repeatType = \"loop\", onPlay, onStop, onComplete, onUpdate, ...options }) {\n    let speed = 1;\n    let hasStopped = false;\n    let resolveFinishedPromise;\n    let currentFinishedPromise;\n    /**\n     * Resolve the current Promise every time we enter the\n     * finished state. This is WAAPI-compatible behaviour.\n     */\n    const updateFinishedPromise = () => {\n        currentFinishedPromise = new Promise((resolve) => {\n            resolveFinishedPromise = resolve;\n        });\n    };\n    // Create the first finished promise\n    updateFinishedPromise();\n    let animationDriver;\n    const generatorFactory = types[type] || _generators_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__.keyframes;\n    /**\n     * If this isn't the keyframes generator and we've been provided\n     * strings as keyframes, we need to interpolate these.\n     */\n    let mapNumbersToKeyframes;\n    if (generatorFactory !== _generators_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__.keyframes &&\n        typeof keyframes$1[0] !== \"number\") {\n        if (true) {\n            (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_4__.invariant)(keyframes$1.length === 2, `Only two keyframes currently supported with spring and inertia animations. Trying to animate ${keyframes$1}`);\n        }\n        mapNumbersToKeyframes = (0,_utils_interpolate_mjs__WEBPACK_IMPORTED_MODULE_5__.interpolate)([0, 100], keyframes$1, {\n            clamp: false,\n        });\n        keyframes$1 = [0, 100];\n    }\n    const generator = generatorFactory({ ...options, keyframes: keyframes$1 });\n    let mirroredGenerator;\n    if (repeatType === \"mirror\") {\n        mirroredGenerator = generatorFactory({\n            ...options,\n            keyframes: [...keyframes$1].reverse(),\n            velocity: -(options.velocity || 0),\n        });\n    }\n    let playState = \"idle\";\n    let holdTime = null;\n    let startTime = null;\n    let cancelTime = null;\n    /**\n     * If duration is undefined and we have repeat options,\n     * we need to calculate a duration from the generator.\n     *\n     * We set it to the generator itself to cache the duration.\n     * Any timeline resolver will need to have already precalculated\n     * the duration by this step.\n     */\n    if (generator.calculatedDuration === null && repeat) {\n        generator.calculatedDuration = (0,_generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_6__.calcGeneratorDuration)(generator);\n    }\n    const { calculatedDuration } = generator;\n    let resolvedDuration = Infinity;\n    let totalDuration = Infinity;\n    if (calculatedDuration !== null) {\n        resolvedDuration = calculatedDuration + repeatDelay;\n        totalDuration = resolvedDuration * (repeat + 1) - repeatDelay;\n    }\n    let currentTime = 0;\n    const tick = (timestamp) => {\n        if (startTime === null)\n            return;\n        /**\n         * requestAnimationFrame timestamps can come through as lower than\n         * the startTime as set by performance.now(). Here we prevent this,\n         * though in the future it could be possible to make setting startTime\n         * a pending operation that gets resolved here.\n         */\n        if (speed > 0)\n            startTime = Math.min(startTime, timestamp);\n        if (speed < 0)\n            startTime = Math.min(timestamp - totalDuration / speed, startTime);\n        if (holdTime !== null) {\n            currentTime = holdTime;\n        }\n        else {\n            // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n            // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n            // example.\n            currentTime = Math.round(timestamp - startTime) * speed;\n        }\n        // Rebase on delay\n        const timeWithoutDelay = currentTime - delay * (speed >= 0 ? 1 : -1);\n        const isInDelayPhase = speed >= 0 ? timeWithoutDelay < 0 : timeWithoutDelay > totalDuration;\n        currentTime = Math.max(timeWithoutDelay, 0);\n        /**\n         * If this animation has finished, set the current time\n         * to the total duration.\n         */\n        if (playState === \"finished\" && holdTime === null) {\n            currentTime = totalDuration;\n        }\n        let elapsed = currentTime;\n        let frameGenerator = generator;\n        if (repeat) {\n            /**\n             * Get the current progress (0-1) of the animation. If t is >\n             * than duration we'll get values like 2.5 (midway through the\n             * third iteration)\n             */\n            const progress = Math.min(currentTime, totalDuration) / resolvedDuration;\n            /**\n             * Get the current iteration (0 indexed). For instance the floor of\n             * 2.5 is 2.\n             */\n            let currentIteration = Math.floor(progress);\n            /**\n             * Get the current progress of the iteration by taking the remainder\n             * so 2.5 is 0.5 through iteration 2\n             */\n            let iterationProgress = progress % 1.0;\n            /**\n             * If iteration progress is 1 we count that as the end\n             * of the previous iteration.\n             */\n            if (!iterationProgress && progress >= 1) {\n                iterationProgress = 1;\n            }\n            iterationProgress === 1 && currentIteration--;\n            currentIteration = Math.min(currentIteration, repeat + 1);\n            /**\n             * Reverse progress if we're not running in \"normal\" direction\n             */\n            const isOddIteration = Boolean(currentIteration % 2);\n            if (isOddIteration) {\n                if (repeatType === \"reverse\") {\n                    iterationProgress = 1 - iterationProgress;\n                    if (repeatDelay) {\n                        iterationProgress -= repeatDelay / resolvedDuration;\n                    }\n                }\n                else if (repeatType === \"mirror\") {\n                    frameGenerator = mirroredGenerator;\n                }\n            }\n            elapsed = (0,_utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_7__.clamp)(0, 1, iterationProgress) * resolvedDuration;\n        }\n        /**\n         * If we're in negative time, set state as the initial keyframe.\n         * This prevents delay: x, duration: 0 animations from finishing\n         * instantly.\n         */\n        const state = isInDelayPhase\n            ? { done: false, value: keyframes$1[0] }\n            : frameGenerator.next(elapsed);\n        if (mapNumbersToKeyframes) {\n            state.value = mapNumbersToKeyframes(state.value);\n        }\n        let { done } = state;\n        if (!isInDelayPhase && calculatedDuration !== null) {\n            done = speed >= 0 ? currentTime >= totalDuration : currentTime <= 0;\n        }\n        const isAnimationFinished = holdTime === null &&\n            (playState === \"finished\" || (playState === \"running\" && done));\n        if (onUpdate) {\n            onUpdate(state.value);\n        }\n        if (isAnimationFinished) {\n            finish();\n        }\n        return state;\n    };\n    const stopAnimationDriver = () => {\n        animationDriver && animationDriver.stop();\n        animationDriver = undefined;\n    };\n    const cancel = () => {\n        playState = \"idle\";\n        stopAnimationDriver();\n        resolveFinishedPromise();\n        updateFinishedPromise();\n        startTime = cancelTime = null;\n    };\n    const finish = () => {\n        playState = \"finished\";\n        onComplete && onComplete();\n        stopAnimationDriver();\n        resolveFinishedPromise();\n    };\n    const play = () => {\n        if (hasStopped)\n            return;\n        if (!animationDriver)\n            animationDriver = driver(tick);\n        const now = animationDriver.now();\n        onPlay && onPlay();\n        if (holdTime !== null) {\n            startTime = now - holdTime;\n        }\n        else if (!startTime || playState === \"finished\") {\n            startTime = now;\n        }\n        if (playState === \"finished\") {\n            updateFinishedPromise();\n        }\n        cancelTime = startTime;\n        holdTime = null;\n        /**\n         * Set playState to running only after we've used it in\n         * the previous logic.\n         */\n        playState = \"running\";\n        animationDriver.start();\n    };\n    if (autoplay) {\n        play();\n    }\n    const controls = {\n        then(resolve, reject) {\n            return currentFinishedPromise.then(resolve, reject);\n        },\n        get time() {\n            return (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_8__.millisecondsToSeconds)(currentTime);\n        },\n        set time(newTime) {\n            newTime = (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_8__.secondsToMilliseconds)(newTime);\n            currentTime = newTime;\n            if (holdTime !== null || !animationDriver || speed === 0) {\n                holdTime = newTime;\n            }\n            else {\n                startTime = animationDriver.now() - newTime / speed;\n            }\n        },\n        get duration() {\n            const duration = generator.calculatedDuration === null\n                ? (0,_generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_6__.calcGeneratorDuration)(generator)\n                : generator.calculatedDuration;\n            return (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_8__.millisecondsToSeconds)(duration);\n        },\n        get speed() {\n            return speed;\n        },\n        set speed(newSpeed) {\n            if (newSpeed === speed || !animationDriver)\n                return;\n            speed = newSpeed;\n            controls.time = (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_8__.millisecondsToSeconds)(currentTime);\n        },\n        get state() {\n            return playState;\n        },\n        play,\n        pause: () => {\n            playState = \"paused\";\n            holdTime = currentTime;\n        },\n        stop: () => {\n            hasStopped = true;\n            if (playState === \"idle\")\n                return;\n            playState = \"idle\";\n            onStop && onStop();\n            cancel();\n        },\n        cancel: () => {\n            if (cancelTime !== null)\n                tick(cancelTime);\n            cancel();\n        },\n        complete: () => {\n            playState = \"finished\";\n        },\n        sample: (elapsed) => {\n            startTime = 0;\n            return tick(elapsed);\n        },\n    };\n    return controls;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/create-accelerated-animation.mjs":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animators/waapi/create-accelerated-animation.mjs ***!
  \*******************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAcceleratedAnimation: function() { return /* binding */ createAcceleratedAnimation; }\n/* harmony export */ });\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/index.mjs\");\n/* harmony import */ var _easing_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./easing.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/easing.mjs\");\n/* harmony import */ var _utils_get_final_keyframe_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/get-final-keyframe.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs\");\n/* harmony import */ var _js_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../js/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/index.mjs\");\n/* harmony import */ var _utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../utils/time-conversion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/time-conversion.mjs\");\n/* harmony import */ var _utils_memo_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/memo.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/memo.mjs\");\n/* harmony import */ var _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n\n\n\n\nconst supportsWaapi = (0,_utils_memo_mjs__WEBPACK_IMPORTED_MODULE_0__.memo)(() => Object.hasOwnProperty.call(Element.prototype, \"animate\"));\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\n    \"opacity\",\n    \"clipPath\",\n    \"filter\",\n    \"transform\",\n    \"backgroundColor\",\n]);\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\n/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxDuration = 20000;\nconst requiresPregeneratedKeyframes = (valueName, options) => options.type === \"spring\" ||\n    valueName === \"backgroundColor\" ||\n    !(0,_easing_mjs__WEBPACK_IMPORTED_MODULE_1__.isWaapiSupportedEasing)(options.ease);\nfunction createAcceleratedAnimation(value, valueName, { onUpdate, onComplete, ...options }) {\n    const canAccelerateAnimation = supportsWaapi() &&\n        acceleratedValues.has(valueName) &&\n        !options.repeatDelay &&\n        options.repeatType !== \"mirror\" &&\n        options.damping !== 0 &&\n        options.type !== \"inertia\";\n    if (!canAccelerateAnimation)\n        return false;\n    /**\n     * TODO: Unify with js/index\n     */\n    let hasStopped = false;\n    let resolveFinishedPromise;\n    let currentFinishedPromise;\n    /**\n     * Cancelling an animation will write to the DOM. For safety we want to defer\n     * this until the next `update` frame lifecycle. This flag tracks whether we\n     * have a pending cancel, if so we shouldn't allow animations to finish.\n     */\n    let pendingCancel = false;\n    /**\n     * Resolve the current Promise every time we enter the\n     * finished state. This is WAAPI-compatible behaviour.\n     */\n    const updateFinishedPromise = () => {\n        currentFinishedPromise = new Promise((resolve) => {\n            resolveFinishedPromise = resolve;\n        });\n    };\n    // Create the first finished promise\n    updateFinishedPromise();\n    let { keyframes, duration = 300, ease, times } = options;\n    /**\n     * If this animation needs pre-generated keyframes then generate.\n     */\n    if (requiresPregeneratedKeyframes(valueName, options)) {\n        const sampleAnimation = (0,_js_index_mjs__WEBPACK_IMPORTED_MODULE_2__.animateValue)({\n            ...options,\n            repeat: 0,\n            delay: 0,\n        });\n        let state = { done: false, value: keyframes[0] };\n        const pregeneratedKeyframes = [];\n        /**\n         * Bail after 20 seconds of pre-generated keyframes as it's likely\n         * we're heading for an infinite loop.\n         */\n        let t = 0;\n        while (!state.done && t < maxDuration) {\n            state = sampleAnimation.sample(t);\n            pregeneratedKeyframes.push(state.value);\n            t += sampleDelta;\n        }\n        times = undefined;\n        keyframes = pregeneratedKeyframes;\n        duration = t - sampleDelta;\n        ease = \"linear\";\n    }\n    const animation = (0,_index_mjs__WEBPACK_IMPORTED_MODULE_3__.animateStyle)(value.owner.current, valueName, keyframes, {\n        ...options,\n        duration,\n        /**\n         * This function is currently not called if ease is provided\n         * as a function so the cast is safe.\n         *\n         * However it would be possible for a future refinement to port\n         * in easing pregeneration from Motion One for browsers that\n         * support the upcoming `linear()` easing function.\n         */\n        ease: ease,\n        times,\n    });\n    const cancelAnimation = () => {\n        pendingCancel = false;\n        animation.cancel();\n    };\n    const safeCancel = () => {\n        pendingCancel = true;\n        _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_4__.frame.update(cancelAnimation);\n        resolveFinishedPromise();\n        updateFinishedPromise();\n    };\n    /**\n     * Prefer the `onfinish` prop as it's more widely supported than\n     * the `finished` promise.\n     *\n     * Here, we synchronously set the provided MotionValue to the end\n     * keyframe. If we didn't, when the WAAPI animation is finished it would\n     * be removed from the element which would then revert to its old styles.\n     */\n    animation.onfinish = () => {\n        if (pendingCancel)\n            return;\n        value.set((0,_utils_get_final_keyframe_mjs__WEBPACK_IMPORTED_MODULE_5__.getFinalKeyframe)(keyframes, options));\n        onComplete && onComplete();\n        safeCancel();\n    };\n    /**\n     * Animation interrupt callback.\n     */\n    const controls = {\n        then(resolve, reject) {\n            return currentFinishedPromise.then(resolve, reject);\n        },\n        attachTimeline(timeline) {\n            animation.timeline = timeline;\n            animation.onfinish = null;\n            return _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_6__.noop;\n        },\n        get time() {\n            return (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_7__.millisecondsToSeconds)(animation.currentTime || 0);\n        },\n        set time(newTime) {\n            animation.currentTime = (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_7__.secondsToMilliseconds)(newTime);\n        },\n        get speed() {\n            return animation.playbackRate;\n        },\n        set speed(newSpeed) {\n            animation.playbackRate = newSpeed;\n        },\n        get duration() {\n            return (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_7__.millisecondsToSeconds)(duration);\n        },\n        play: () => {\n            if (hasStopped)\n                return;\n            animation.play();\n            /**\n             * Cancel any pending cancel tasks\n             */\n            (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_4__.cancelFrame)(cancelAnimation);\n        },\n        pause: () => animation.pause(),\n        stop: () => {\n            hasStopped = true;\n            if (animation.playState === \"idle\")\n                return;\n            /**\n             * WAAPI doesn't natively have any interruption capabilities.\n             *\n             * Rather than read commited styles back out of the DOM, we can\n             * create a renderless JS animation and sample it twice to calculate\n             * its current value, \"previous\" value, and therefore allow\n             * Motion to calculate velocity for any subsequent animation.\n             */\n            const { currentTime } = animation;\n            if (currentTime) {\n                const sampleAnimation = (0,_js_index_mjs__WEBPACK_IMPORTED_MODULE_2__.animateValue)({\n                    ...options,\n                    autoplay: false,\n                });\n                value.setWithVelocity(sampleAnimation.sample(currentTime - sampleDelta).value, sampleAnimation.sample(currentTime).value, sampleDelta);\n            }\n            safeCancel();\n        },\n        complete: () => {\n            if (pendingCancel)\n                return;\n            animation.finish();\n        },\n        cancel: safeCancel,\n    };\n    return controls;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/create-accelerated-animation.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/easing.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animators/waapi/easing.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cubicBezierAsString: function() { return /* binding */ cubicBezierAsString; },\n/* harmony export */   isWaapiSupportedEasing: function() { return /* binding */ isWaapiSupportedEasing; },\n/* harmony export */   mapEasingToNativeEasing: function() { return /* binding */ mapEasingToNativeEasing; },\n/* harmony export */   supportedWaapiEasing: function() { return /* binding */ supportedWaapiEasing; }\n/* harmony export */ });\n/* harmony import */ var _easing_utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../easing/utils/is-bezier-definition.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/utils/is-bezier-definition.mjs\");\n\n\nfunction isWaapiSupportedEasing(easing) {\n    return Boolean(!easing ||\n        (typeof easing === \"string\" && supportedWaapiEasing[easing]) ||\n        (0,_easing_utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__.isBezierDefinition)(easing) ||\n        (Array.isArray(easing) && easing.every(isWaapiSupportedEasing)));\n}\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: cubicBezierAsString([0, 0.65, 0.55, 1]),\n    circOut: cubicBezierAsString([0.55, 0, 1, 0.45]),\n    backIn: cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n    backOut: cubicBezierAsString([0.33, 1.53, 0.69, 0.99]),\n};\nfunction mapEasingToNativeEasing(easing) {\n    if (!easing)\n        return undefined;\n    return (0,_easing_utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__.isBezierDefinition)(easing)\n        ? cubicBezierAsString(easing)\n        : Array.isArray(easing)\n            ? easing.map(mapEasingToNativeEasing)\n            : supportedWaapiEasing[easing];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/easing.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/index.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animators/waapi/index.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateStyle: function() { return /* binding */ animateStyle; }\n/* harmony export */ });\n/* harmony import */ var _easing_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./easing.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/easing.mjs\");\n\n\nfunction animateStyle(element, valueName, keyframes, { delay = 0, duration, repeat = 0, repeatType = \"loop\", ease, times, } = {}) {\n    const keyframeOptions = { [valueName]: keyframes };\n    if (times)\n        keyframeOptions.offset = times;\n    const easing = (0,_easing_mjs__WEBPACK_IMPORTED_MODULE_0__.mapEasingToNativeEasing)(ease);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */\n    if (Array.isArray(easing))\n        keyframeOptions.easing = easing;\n    return element.animate(keyframeOptions, {\n        delay,\n        duration,\n        easing: !Array.isArray(easing) ? easing : \"linear\",\n        fill: \"both\",\n        iterations: repeat + 1,\n        direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\",\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2FuaW1hdG9ycy93YWFwaS9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUQ7O0FBRXZELHVEQUF1RCxxRUFBcUUsSUFBSTtBQUNoSSw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBLG1CQUFtQixvRUFBdUI7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUV3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2FuaW1hdGlvbi9hbmltYXRvcnMvd2FhcGkvaW5kZXgubWpzP2E0MTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbWFwRWFzaW5nVG9OYXRpdmVFYXNpbmcgfSBmcm9tICcuL2Vhc2luZy5tanMnO1xuXG5mdW5jdGlvbiBhbmltYXRlU3R5bGUoZWxlbWVudCwgdmFsdWVOYW1lLCBrZXlmcmFtZXMsIHsgZGVsYXkgPSAwLCBkdXJhdGlvbiwgcmVwZWF0ID0gMCwgcmVwZWF0VHlwZSA9IFwibG9vcFwiLCBlYXNlLCB0aW1lcywgfSA9IHt9KSB7XG4gICAgY29uc3Qga2V5ZnJhbWVPcHRpb25zID0geyBbdmFsdWVOYW1lXToga2V5ZnJhbWVzIH07XG4gICAgaWYgKHRpbWVzKVxuICAgICAgICBrZXlmcmFtZU9wdGlvbnMub2Zmc2V0ID0gdGltZXM7XG4gICAgY29uc3QgZWFzaW5nID0gbWFwRWFzaW5nVG9OYXRpdmVFYXNpbmcoZWFzZSk7XG4gICAgLyoqXG4gICAgICogSWYgdGhpcyBpcyBhbiBlYXNpbmcgYXJyYXksIGFwcGx5IHRvIGtleWZyYW1lcywgbm90IGFuaW1hdGlvbiBhcyBhIHdob2xlXG4gICAgICovXG4gICAgaWYgKEFycmF5LmlzQXJyYXkoZWFzaW5nKSlcbiAgICAgICAga2V5ZnJhbWVPcHRpb25zLmVhc2luZyA9IGVhc2luZztcbiAgICByZXR1cm4gZWxlbWVudC5hbmltYXRlKGtleWZyYW1lT3B0aW9ucywge1xuICAgICAgICBkZWxheSxcbiAgICAgICAgZHVyYXRpb24sXG4gICAgICAgIGVhc2luZzogIUFycmF5LmlzQXJyYXkoZWFzaW5nKSA/IGVhc2luZyA6IFwibGluZWFyXCIsXG4gICAgICAgIGZpbGw6IFwiYm90aFwiLFxuICAgICAgICBpdGVyYXRpb25zOiByZXBlYXQgKyAxLFxuICAgICAgICBkaXJlY3Rpb246IHJlcGVhdFR5cGUgPT09IFwicmV2ZXJzZVwiID8gXCJhbHRlcm5hdGVcIiA6IFwibm9ybWFsXCIsXG4gICAgfSk7XG59XG5cbmV4cG9ydCB7IGFuaW1hdGVTdHlsZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs":
/*!***************************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs ***!
  \***************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFinalKeyframe: function() { return /* binding */ getFinalKeyframe; }\n/* harmony export */ });\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }) {\n    const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1\n        ? 0\n        : keyframes.length - 1;\n    return keyframes[index];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2FuaW1hdG9ycy93YWFwaS91dGlscy9nZXQtZmluYWwta2V5ZnJhbWUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSx1Q0FBdUMsNkJBQTZCO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTRCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2FuaW1hdG9ycy93YWFwaS91dGlscy9nZXQtZmluYWwta2V5ZnJhbWUubWpzPzA1ZTUiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZ2V0RmluYWxLZXlmcmFtZShrZXlmcmFtZXMsIHsgcmVwZWF0LCByZXBlYXRUeXBlID0gXCJsb29wXCIgfSkge1xuICAgIGNvbnN0IGluZGV4ID0gcmVwZWF0ICYmIHJlcGVhdFR5cGUgIT09IFwibG9vcFwiICYmIHJlcGVhdCAlIDIgPT09IDFcbiAgICAgICAgPyAwXG4gICAgICAgIDoga2V5ZnJhbWVzLmxlbmd0aCAtIDE7XG4gICAgcmV0dXJuIGtleWZyYW1lc1tpbmRleF07XG59XG5cbmV4cG9ydCB7IGdldEZpbmFsS2V5ZnJhbWUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs\n"));

/***/ })

}]);